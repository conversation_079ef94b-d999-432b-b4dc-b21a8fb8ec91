import OrchestrationApiService, { apiEndpoints } from '~/js/services/OrchestrationApiService.js';

export interface ActionContext {
    formState: any;
    validator: any;
    assistantId: string;
}

export function createFormActions(context: ActionContext) {
    const { formState, validator, assistantId } = context;

    async function handleAction(action: any) {
        if (!action) return;

        switch (action.type) {
            case 'navigate':
                formState.navigateTo(action.target, action.setValues || {});
                break;

            case 'submit': {
                const validation = validator.validateCurrentPage(formState.currentPage, formState.formData);
                if (validation.isValid) {
                    // si l'action a une api associée, on lance l'appel
                    if (action.apiCallId) {
                        await submitFormToAPI(action);
                    } else {
                        formState.navigateTo(action.target);
                    }
                } else {
                    formState.setError(validation.errors[0]);
                }
                break;
            }

            case 'copy':
                copyToClipboard(action.target, action.successMessage);
                break;

            case 'export':
                exportContent(action.target, action.options);
                break;
        }

        // @todo donner la possibilité de chainer des actions
        // if (action.action) {
        //     await handleAction(action.action);
        // }
    }

    async function submitFormToAPI(action: any) {
        try {
            formState.setLoading(true);
            formState.setError(null);
            formState.resetStreamingResponse();

            // recherche de la configuration de l'appel à l'API
            const apiCallId = action?.apiCallId ?? null;
            const apiCalls = formState.currentPage?.apiCalls ?? [];
            const apiCall = apiCalls.find((config: any) => config.id === apiCallId);

            if (!apiCall) {
                throw new Error(`Configuration de l'API non trouvée: ${apiCallId}`);
            }

            const requestData = buildRequestData(apiCall);

            formState.navigateTo(action.target);

            // execution de la requête avec les données issues du formulaire
            await request(action, apiCall, requestData);
        } catch (err: any) {
            formState.setError(err.message);
            console.error('Erreur lors de la soumission du formulaire:', err);
        } finally {
            formState.setLoading(false);
        }
    }

    function buildRequestData(apiCall: any): Record<string, any> {
        const requestData: Record<string, any> = {};
        const { requestMapping } = apiCall;

        for (const [key, value] of Object.entries(requestMapping)) {
            if (Array.isArray(value)) {
                for (const field of value) {
                    if (formState.formData[field]) {
                        requestData[key] = formState.formData[field];
                        break;
                    }
                }
            } else {
                requestData[key] = formState.formData[value as string];
            }
        }

        return requestData;
    }

    async function request(action: any, apiCall: any, requestData: Record<string, any>) {
        const endpointConfig = apiEndpoints?.[apiCall.endpoint] ?? null;
        switch (endpointConfig.type) {
            case 'stream':
                await handleStreamingRequest(action, apiCall, requestData);
                break;
            case 'standard':
                await handleStandardRequest(action, apiCall, requestData);
                break;
            case 'void':
                await handleVoidRequest(action, apiCall, requestData);
                break;
            default:
                throw new Error(`Type de endpoint API non supporté: ${formState.currentPage.api.endpoint}`);
        }
    }

    async function handleStandardRequest(action: any, apiCall: any, requestData: Record<string, any>) {
        const response = await OrchestrationApiService.call(apiCall, assistantId, requestData);

        const { responseMapping } = apiCall;
        const mappedData: Record<string, any> = {};

        for (const [key, field] of Object.entries(responseMapping)) {
            mappedData[field as string] = response[key];
        }

        formState.setFormData(mappedData);
    }

    async function handleStreamingRequest(action: any, apiCall: any, requestData: Record<string, any>) {
        formState.setStreaming(true);

        await OrchestrationApiService.streamedCall(
            apiCall,
            assistantId,
            requestData,
            (token: string) => {
                formState.appendStreamingResponse(token);
            },
            () => {
                formState.setStreaming(false);
                // @todo rendre générique
                formState.updateFormData('generated-report', formState.streamingResponse);
            },
            (err: any) => {
                formState.setError(err.message);
                formState.setStreaming(false);
            }
        );
    }

    async function handleVoidRequest(action: any, apiCall: any, requestData: Record<string, any>) {
        const response = await OrchestrationApiService.call(apiCall, assistantId, requestData);
        console.log('response', response);

        // Pour les requêtes createRAG, on stocke le retriever_id
        if (apiCall.endpoint === 'createRAG' && response.retriever_id) {
            const { responseMapping } = apiCall;
            const mappedData: Record<string, any> = {};

            for (const [key, field] of Object.entries(responseMapping)) {
                mappedData[field as string] = response[key];
            }

            formState.setFormData(mappedData);
        }
    }

    async function handleChatMessage(apiCall: any, message: string) {
        try {
            formState.setStreaming(true);
            let streamingResponse = '';

            const requestData = buildRequestData(apiCall);
            // Ajouter le message à la requête
            requestData.question = message;

            // TODO refaire plus générique
            await OrchestrationApiService.streamRAG(
                assistantId,
                requestData.ragId,
                requestData,
                (token: string) => {
                    // Accumuler la réponse
                    streamingResponse += token;

                    // Mettre à jour le dernier message de l'assistant dans le chat
                    if (typeof window !== 'undefined' && (window as any).chatComponent) {
                        (window as any).chatComponent.updateLastAssistantMessage(streamingResponse);
                    }

                    // Aussi mettre à jour le formState pour la compatibilité
                    formState.appendStreamingResponse(token);
                },
                () => {
                    formState.setStreaming(false);
                    // Terminer le chargement dans le chat
                    if (typeof window !== 'undefined' && (window as any).chatComponent) {
                        (window as any).chatComponent.finishLoading();
                    }
                    // Optionnel : mettre à jour les données du formulaire avec la réponse finale
                    formState.updateFormData('chat', streamingResponse);
                },
                (err: any) => {
                    // En cas d'erreur, afficher l'erreur dans le chat
                    if (typeof window !== 'undefined' && (window as any).chatComponent) {
                        (window as any).chatComponent.updateLastAssistantMessage(`Erreur: ${err.message}`);
                        (window as any).chatComponent.finishLoading();
                    }
                    formState.setError(err.message);
                    formState.setStreaming(false);
                }
            );
        } catch (err: any) {
            // En cas d'erreur, afficher l'erreur dans le chat
            if (typeof window !== 'undefined' && (window as any).chatComponent) {
                (window as any).chatComponent.updateLastAssistantMessage(`Erreur: ${err.message}`);
                (window as any).chatComponent.finishLoading();
            }
            formState.setError(err.message);
            formState.setStreaming(false);
        }
    }

    function copyToClipboard(targetField: string, successMessage?: string) {
        const text = formState.formData[targetField];
        if (!text) return;

        navigator.clipboard
            .writeText(text)
            .then(() => {
                alert(successMessage || 'Copié dans le presse-papier !');
            })
            .catch((err) => {
                console.error('Erreur lors de la copie:', err);
            });
    }

    function exportContent(targetField: string, options: any = {}) {
        const content = formState.formData[targetField];
        if (!content) return;

        const filename = options.filename || 'export.txt';
        const format = options.format || 'text';

        const mimeTypes: Record<string, string> = {
            text: 'text/plain',
            html: 'text/html',
            json: 'application/json',
        };

        const blob = new Blob([content], { type: mimeTypes[format] });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    return {
        handleAction,
        submitFormToAPI,
        copyToClipboard,
        exportContent,
        handleChatMessage,
    };
}
