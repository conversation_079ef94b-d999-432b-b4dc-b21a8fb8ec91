import Button from './ui/Button.svelte';
import ButtonGroup from './ui/ButtonGroup.svelte';
import FileUpload from './ui/FileUpload.svelte';
import RadioButton from './ui/RadioButton.svelte';
import RichTextarea from './ui/RichTextarea.svelte';
import Select from './ui/Select.svelte';
import Text from './ui/Text.svelte';
import ModelSelector from './ui/ModelSelector.svelte';

export const componentRegistry = {
    Text,
    Button,
    ButtonGroup,
    RichTextarea,
    Select,
    FileUpload,
    RadioButton,
    ModelSelector,
} as const;

export type ComponentType = keyof typeof componentRegistry;

export function getComponent(componentName: string) {
    return componentRegistry[componentName as ComponentType];
}
