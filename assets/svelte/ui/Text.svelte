<script lang="ts">
const {
    content = '',
    variant = 'paragraph', // paragraph, heading, caption
    size = 'medium', // small, medium, large
    align = 'left', // left, center, right
}: {
    content: string;
    variant: 'paragraph' | 'heading' | 'caption';
    size: 'small' | 'medium' | 'large';
    align: 'left' | 'center' | 'right';
} = $props();

const variantMap = {
    paragraph: 'p',
    heading: 'h3',
    caption: 'span',
};

const sizeMap = {
    small: 'text-sm',
    medium: 'text-md',
    large: 'text-lg',
};

const alignMap = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
};

const Tag = variantMap[variant] || 'p';
</script>

<svelte:element
    this={Tag}
    class="{sizeMap[size]} {alignMap[align]}"
>
    {content}
</svelte:element>

<style>
    .text-sm {
        font-size: 0.875rem;
    }

    .text-md {
        font-size: 1rem;
    }

    .text-lg {
        font-size: 1.25rem;
    }

    .text-left {
        text-align: left;
    }

    .text-center {
        text-align: center;
    }

    .text-right {
        text-align: right;
    }
</style>
