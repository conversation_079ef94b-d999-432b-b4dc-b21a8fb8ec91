<script lang="ts">
    let inputValue: string = $state('');
    let messages: { author: string; content: string }[] = $state([]);

    function handleSendMessage() {
        if (inputValue.trim() === '') return;

        messages = [
            ...messages,
            {
                author: 'user',
                content: inputValue,
            },
        ];

        inputValue = '';
    }
</script>

<div class="chat-container">
    <div class="message-container">
        {#if messages}
            {#each messages as message}
                <div class="message {message.author}">
                    <div class="message-content">
                        {message.content}
                    </div>
                </div>
            {/each}
        {/if}
    </div>
    <div class="input-container">
        <textarea
            class="input"
            placeholder="Type your message..."
            bind:value={inputValue}
            onkeydown={(event) => {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    handleSendMessage();
                }
            }}
        ></textarea>
        <button class="send-button" onclick={handleSendMessage}>Demander</button>
    </div>
</div>

<style>
    .message-container {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        padding: 1rem;
        border-radius: 0.5rem;
        overflow-y: auto;
    }

    .message {
        display: flex;
        flex-direction: row;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .message.user {
        flex-direction: row-reverse;
    }

    .message-content {
        background-color: #f2f2f2;
        padding: 0.5rem;
        border-radius: 0.5rem;
        max-width: 70%;
        word-wrap: break-word;
    }

    .message.user .message-content {
        background-color: #e0e0e0;
    }

    .input-container {
        display: flex;
        flex-direction: row;
        gap: 0.5rem;
        align-items: flex-start;
        padding: 1rem;
    }

    .input {
        flex: 1;
        min-height: 4rem;
        font-size: 1rem;
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 0.5rem;
        resize: vertical;
    }

    .send-button {
        padding: 0.5rem 1rem;
        font-size: 1rem;
        border: none;
        border-radius: 0.5rem;
        cursor: pointer;
        background-color: #3498db;
        color: white;
    }

    .send-button:hover {
        background-color: #2980b9;
    }
</style>
