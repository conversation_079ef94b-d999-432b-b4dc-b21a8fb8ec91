<script lang="ts">
    let {
        value = '',
        oninput = () => {},
        formData = {},
        onMessageSent = () => {},
    }: {
        value: string;
        oninput: (event: { detail: string }) => void;
        formData: Record<string, any>;
        onMessageSent: (message: string) => void;
    } = $props();

    let inputValue: string = $state('');
    let messages: { author: string; content: string }[] = $state([]);
    let isLoading: boolean = $state(false);

    // Initialiser les messages depuis la valeur si elle existe
    $effect(() => {
        if (value && typeof value === 'string' && value !== JSON.stringify(messages)) {
            try {
                const parsedMessages = JSON.parse(value);
                if (Array.isArray(parsedMessages)) {
                    messages = parsedMessages;
                }
            } catch (e) {
                // Si ce n'est pas du JSON valide, ignorer
            }
        }
    });

    function handleSendMessage() {
        if (inputValue.trim() === '' || isLoading) return;

        const userMessage = inputValue.trim();

        // Ajouter le message de l'utilisateur
        messages = [
            ...messages,
            {
                author: 'user',
                content: userMessage,
            },
        ];

        // Activer le mode chargement
        isLoading = true;

        // Notifier le parent du nouveau message
        onMessageSent(userMessage);

        // Mettre à jour la valeur
        oninput({ detail: JSON.stringify(messages) });

        inputValue = '';
    }

    // Fonction pour ajouter un nouveau message de l'assistant
    function addAssistantMessage() {
        messages = [
            ...messages,
            {
                author: 'assistant',
                content: '',
            },
        ];
        oninput({ detail: JSON.stringify(messages) });
    }

    // Fonction pour mettre à jour le dernier message de l'assistant
    function updateLastAssistantMessage(content: string) {
        if (messages.length > 0 && messages[messages.length - 1].author === 'assistant') {
            messages = [
                ...messages.slice(0, -1),
                {
                    author: 'assistant',
                    content: content,
                },
            ];
            oninput({ detail: JSON.stringify(messages) });
        }
    }

    // Fonction pour terminer le chargement
    function finishLoading() {
        isLoading = false;
    }

    // Exposer les fonctions pour le parent
    $effect(() => {
        if (typeof window !== 'undefined') {
            (window as any).chatComponent = {
                addAssistantMessage,
                updateLastAssistantMessage,
                finishLoading,
            };
        }
    });
</script>

<div class="chat-container">
    <div class="message-container">
        {#if messages}
            {#each messages as message}
                <div class="message {message.author}">
                    <div class="message-content">
                        {message.content}
                    </div>
                </div>
            {/each}
        {/if}
    </div>
    <div class="input-container">
        <textarea
            class="input"
            placeholder="Type your message..."
            bind:value={inputValue}
            onkeydown={(event) => {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    handleSendMessage();
                }
            }}
        ></textarea>
        <button
            class="send-button"
            onclick={handleSendMessage}
            disabled={isLoading}
        >
            {isLoading ? 'Génération...' : 'Demander'}
        </button>
    </div>
</div>

<style>
    .message-container {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        padding: 1rem;
        border-radius: 0.5rem;
        overflow-y: auto;
    }

    .message {
        display: flex;
        flex-direction: row;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .message.user {
        flex-direction: row-reverse;
    }

    .message-content {
        background-color: #f2f2f2;
        padding: 0.5rem;
        border-radius: 0.5rem;
        max-width: 70%;
        word-wrap: break-word;
    }

    .message.user .message-content {
        background-color: #e0e0e0;
    }

    .input-container {
        display: flex;
        flex-direction: row;
        gap: 0.5rem;
        align-items: flex-start;
        padding: 1rem;
    }

    .input {
        flex: 1;
        min-height: 4rem;
        font-size: 1rem;
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 0.5rem;
        resize: vertical;
    }

    .send-button {
        padding: 0.5rem 1rem;
        font-size: 1rem;
        border: none;
        border-radius: 0.5rem;
        cursor: pointer;
        background-color: #3498db;
        color: white;
    }

    .send-button:hover:not(:disabled) {
        background-color: #2980b9;
    }

    .send-button:disabled {
        background-color: #bdc3c7;
        cursor: not-allowed;
        opacity: 0.6;
    }
</style>
