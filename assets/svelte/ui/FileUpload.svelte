<script lang="ts">
import OrchestrationApiService from '~/js/services/OrchestrationApiService.ts';
import { getContext } from 'svelte';

let {
    id = '',
    label = '',
    accept = '*/*',
    required = false,
    maxSize = 5000000, // 5MB par défaut
    dropzoneText = 'Déposez votre fichier ici ou cliquez pour parcourir',
    uploadedFile = null,
    value = '',
    oninput = () => {},
}: {
    id?: string;
    label?: string;
    accept?: string;
    required?: boolean;
    maxSize?: number;
    dropzoneText?: string;
    uploadedFile?: File | null;
    value: string;
    oninput: (event: { detail: string }) => void;
} = $props();

const assistantId: string = getContext('assistantId');

let dragActive = $state(false);
let fileError = $state(null);
let fileInput: HTMLInputElement;

function handleDragEnter(e) {
    e.preventDefault();
    e.stopPropagation();
    dragActive = true;
}

function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    dragActive = false;
}

function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    dragActive = true;
}

function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    dragActive = false;

    if (e.dataTransfer.files?.[0]) {
        handleFiles(e.dataTransfer.files);
    }
}

function handleChange(e) {
    if (e.target.files?.[0]) {
        handleFiles(e.target.files);
    }
}

async function handleFiles(files) {
    const file = files[0];

    // Vérifier le type de fichier
    if (accept !== '*/*') {
        const acceptTypes = accept.split(',');
        const fileType = file.type;

        const isAccepted = acceptTypes.some((type) => {
            if (type.includes('/*')) {
                const mainType = type.split('/')[0];
                return fileType.startsWith(`${mainType}/`);
            }
            return type === fileType;
        });

        if (!isAccepted) {
            fileError = `Type de fichier non accepté. Veuillez utiliser : ${accept}`;
            return;
        }
    }

    // Vérifier la taille du fichier
    if (file.size > maxSize) {
        const maxSizeMB = maxSize / 1000000;
        fileError = `Fichier trop volumineux. Maximum : ${maxSizeMB} MB`;
        return;
    }

    fileError = null;
    uploadedFile = file;

    if (uploadedFile) {
        await uploadFile(uploadedFile);
    }
}

async function uploadFile(file: File) {
    const response = await OrchestrationApiService.invokeUpload(assistantId, file);
    oninput({ detail: response.path });
}

function openFileSelector() {
    if (fileInput) {
        fileInput.click();
    }
}
</script>

<div class="file-upload-container">
  {#if label}
    <label for={id}>{label}{required ? ' *' : ''}</label>
  {/if}

  <div
    class="dropzone {dragActive ? 'active' : ''} {fileError ? 'error' : ''}"
    ondragenter={handleDragEnter}
    ondragleave={handleDragLeave}
    ondragover={handleDragOver}
    ondrop={handleDrop}
    onclick={openFileSelector}
  >
    <input
      type="file"
      {id}
      {accept}
      {required}
      onchange={handleChange}
      style="display: none;"
      bind:this={fileInput}
    />

    {#if uploadedFile}
      <div class="file-info">
        <span class="file-name">{uploadedFile.name}</span>
        <span class="file-size">({(uploadedFile.size / 1000).toFixed(1)} KB)</span>
      </div>
    {:else}
      <div class="dropzone-text">
        <i class="fa fa-upload"></i>
        <p>{dropzoneText}</p>
      </div>
    {/if}
  </div>

  {#if fileError}
    <div class="error-message">{fileError}</div>
  {/if}
</div>

<style>
  .file-upload-container {
    margin-bottom: 1rem;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .dropzone {
    border: 2px dashed #ddd;
    border-radius: 4px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .dropzone:hover {
    border-color: #3498db;
    background-color: rgba(52, 152, 219, 0.05);
  }

  .dropzone.active {
    border-color: #3498db;
    background-color: rgba(52, 152, 219, 0.1);
  }

  .dropzone.error {
    border-color: #e74c3c;
    background-color: rgba(231, 76, 60, 0.05);
  }

  .dropzone-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: #666;
  }

  .file-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
  }

  .file-name {
    font-weight: 500;
    word-break: break-all;
  }

  .file-size {
    color: #666;
    font-size: 0.875rem;
  }

  .error-message {
    color: #e74c3c;
    font-size: 0.875rem;
    margin-top: 0.5rem;
  }
</style>
