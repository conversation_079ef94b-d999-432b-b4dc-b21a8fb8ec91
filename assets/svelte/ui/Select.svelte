<script lang="ts">
let {
    id = '',
    label = '',
    options = [],
    value = '',
    defaultValue = '',
    required = false,
    disabled = false,
    oninput = () => {},
}: {
    id: string;
    label: string;
    options: { value: string; label: string }[];
    value: string;
    defaultValue: string;
    required: boolean;
    disabled: boolean;
    oninput: (event: CustomEvent<string>) => void;
} = $props();

// Utiliser la valeur par défaut si aucune valeur n'est fournie
$effect(() => {
    if (!value && defaultValue) {
        value = defaultValue;
        oninput({ detail: value });
    }
});

function handleChange(e) {
    const newValue = e.target.value;
    oninput({ detail: newValue });
}
</script>

<div class="select-container">
    {#if label}
        <label for={id}>{label}{required ? ' *' : ''}</label>
    {/if}

    <select
        {id}
        {required}
        {disabled}
        {value}
        oninput={handleChange}
    >
        {#each options as option}
            <option value={option.value}>{option.label}</option>
        {/each}
    </select>
</div>

<style>
    .select-container {
        margin-bottom: 1rem;
    }

    label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    select {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-family: inherit;
        font-size: 1rem;
        background-color: white;
        cursor: pointer;
    }

    select:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    }

    select:disabled {
        background-color: #f9f9f9;
        cursor: not-allowed;
        opacity: 0.6;
    }
</style>
