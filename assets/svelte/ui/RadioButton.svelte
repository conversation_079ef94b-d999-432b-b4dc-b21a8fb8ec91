<script lang="ts">
let {
    id = '',
    name = '',
    label = '',
    value = '',
    checked = false,
    disabled = false,
    oninput = () => {},
}: {
    id: string;
    name: string;
    label: string;
    value: string;
    checked: boolean;
    disabled: boolean;
    oninput: (event: CustomEvent<string>) => void;
} = $props();

function handleChange(e: Event) {
    const target = e.target as HTMLInputElement;
    if (target.checked) {
        oninput({ detail: value });
    }
}
</script>

<div class="radio-container">
    <input
        type="radio"
        {id}
        {name}
        {value}
        {checked}
        {disabled}
        oninput={handleChange}
    />
    {#if label}
        <label for={id}>{label}</label>
    {/if}
</div>

<style>
    .radio-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }

    input[type="radio"] {
        width: 1.125rem;
        height: 1.125rem;
        border: 2px solid #ddd;
        border-radius: 50%;
        appearance: none;
        position: relative;
        cursor: pointer;
        background-color: white;
        transition: all 0.2s ease;
    }

    input[type="radio"]:checked {
        border-color: #3498db;
        background-color: #3498db;
    }

    input[type="radio"]:checked::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 0.375rem;
        height: 0.375rem;
        border-radius: 50%;
        background-color: white;
    }

    input[type="radio"]:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    }

    input[type="radio"]:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    label {
        font-weight: 500;
        cursor: pointer;
        user-select: none;
    }

    input[type="radio"]:disabled + label {
        opacity: 0.6;
        cursor: not-allowed;
    }
</style>