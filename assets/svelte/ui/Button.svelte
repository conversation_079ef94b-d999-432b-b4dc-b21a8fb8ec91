<script lang="ts">
const {
    label = '',
    variant = 'primary',
    size = 'medium',
    icon = null,
    disabled = false,
    onclick = () => {},
}: {
    label: string;
    variant: 'primary' | 'secondary' | 'outline';
    size: 'small' | 'medium' | 'large';
    icon: string;
    disabled: boolean;
    onclick: (event: Event) => void;
} = $props();
</script>

<button
  class="button {variant} {size}"
  {disabled}
  {onclick}
>
  {#if icon}
    <i class="{icon}"></i>
  {/if}
  {label}
</button>

<style>
  .button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
  }

  .button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .primary {
    background-color: #3498db;
    color: white;
  }

  .primary:hover:not(:disabled) {
    background-color: #2980b9;
  }

  .secondary {
    background-color: #95a5a6;
    color: white;
  }

  .secondary:hover:not(:disabled) {
    background-color: #7f8c8d;
  }

  .outline {
    background-color: transparent;
    color: #3498db;
    border: 1px solid #3498db;
  }

  .outline:hover:not(:disabled) {
    background-color: rgba(52, 152, 219, 0.1);
  }

  .small {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }

  .medium {
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }

  .large {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
  }
</style>
