<script lang="ts">
import type { Snippet } from 'svelte';
const {
    children,
    orientation = 'horizontal', // horizontal, vertical
    gap = 'medium', // small, medium, large
    alignment = 'center', // start, center, end
}: {
    children: Snippet;
    orientation: 'horizontal' | 'vertical';
    gap: 'small' | 'medium' | 'large';
    alignment: 'start' | 'center' | 'end';
} = $props();

const gapSizes = {
    small: '0.5rem',
    medium: '1rem',
    large: '1.5rem',
};

const alignments = {
    start: 'flex-start',
    center: 'center',
    end: 'flex-end',
};
</script>

<div
  class="button-group {orientation}"
  style="gap: {gapSizes[gap]}; justify-content: {alignments[alignment]};"
>
  {@render children?.()}
</div>

<style>
  .button-group {
    display: flex;
  }

  .horizontal {
    flex-direction: row;
  }

  .vertical {
    flex-direction: column;
  }
</style>
