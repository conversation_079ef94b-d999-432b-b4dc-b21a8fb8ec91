<script lang="ts">
import FormRenderer from '../FormRenderer.svelte';
import { setContext } from 'svelte';

const {
    formSchema,
    assistantId,
}: {
    formSchema: Record<string, any>;
    assistantId: string;
} = $props();

setContext('assistantId', assistantId);
setContext('formSchema', formSchema);
</script>

<div class="assistant-container">
    <FormRenderer {formSchema} {assistantId} />
</div>

<style>
  .assistant-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }
</style>
