<script lang="ts">
import { onMount } from 'svelte';
import { componentRegistry } from './component-registry.svelte.ts';
import { createFormActions } from './form-actions.svelte.ts';
import { createFormState } from './form-state.svelte.ts';
import { createFormValidator } from './form-validation.svelte.ts';

const {
    formSchema,
    assistantId,
}: {
    formSchema: Record<string, any>;
    assistantId: string;
} = $props();

const formState = createFormState(formSchema);
const validator = createFormValidator();
const actions = createFormActions({ formState, validator, assistantId });

onMount(() => {
    formState.initializeDefaults(formSchema);
});
</script>

{#if formState.debugMode}
    <pre class="border border-1 border-secondary"><code>{JSON.stringify(formState.formData, null, 2)}</code></pre>
{/if}

<div class="form-container">
  <h1>{formSchema.title}</h1>
  <p>{formSchema.description}</p>

  {#if formState.isLoading && !formState.isStreaming}
    <div class="loading">
      <span class="spinner"></span>
      <p>Chargement en cours...</p>
    </div>
  {:else if formState.isStreaming}
    <div class="streaming">
      <span class="spinner"></span>
      <p>Génération du compte-rendu en cours...</p>
    </div>
  {:else if formState.error}
    <div class="error">
      <p>{formState.error}</p>
    </div>
  {/if}

  {#if formState.currentPage}
    <div class="page">
      <h2>{formState.currentPage.title}</h2>

      {#each formState.currentPage.components || [] as component}
        {#if component.component === 'ButtonGroup' && component.children}
          {@const SvelteComponent = componentRegistry[component.component]}
          <SvelteComponent {...component.props}>
            {#each component.children as child}
              {@const ChildSvelteComponent = componentRegistry[child.component]}
              <ChildSvelteComponent
                {...child.props}
                onclick={() => actions.handleAction(child.action)}
              />
            {/each}
          </SvelteComponent>
        {:else if component.component === 'Chat'}
          {@const SvelteComponent = componentRegistry[component.component]}
          <SvelteComponent
            {...component.props}
            value={formState.getBindValue(component)}
            oninput={(e) => formState.updateBindValue(component, e.detail)}
            formData={formState.formData}
            onMessageSent={(message) => {
              // Ajouter immédiatement un message vide pour l'assistant
              if (typeof window !== 'undefined' && (window as any).chatComponent) {
                (window as any).chatComponent.addAssistantMessage();
              }

              // Déclencher l'appel API si configuré
              const apiCall = formState.currentPage.apiCalls?.find(call => call.id === 'ask-question-about-document');
              if (apiCall) {
                actions.handleChatMessage(apiCall, message);
              }
            }}
          />
        {:else}
          {@const SvelteComponent = componentRegistry[component.component]}
          <SvelteComponent
            {...component.props}
            value={formState.getBindValue(component)}
            oninput={(e) => formState.updateBindValue(component, e.detail)}
          />
        {/if}
      {/each}

      {#if formState.currentPage.actions}
        <div class="actions">
          {#each formState.currentPage.actions as action}
            {@const ActionSvelteComponent = componentRegistry[action.component]}
            <ActionSvelteComponent
              {...action.props}
              onclick={() => actions.handleAction(action.action)}
            />
          {/each}
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }

  .page {
    margin-top: 2rem;
  }

  .actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    justify-content: flex-end;
  }

  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 2rem 0;
  }

  .spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 2s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error {
    background-color: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
  }

  .streaming {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 1rem 0;
    background-color: #e3f2fd;
    color: #1565c0;
    padding: 1rem;
    border-radius: 4px;
  }
</style>
