@import "@fortawesome/fontawesome-free/css/all.css";
@import "bootstrap/dist/css/bootstrap.css";

.page-header {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 10px;

    img:first-child {
        border-right: 1px solid #3a3a3a;
        padding-right: 15px;
        margin-right: 15px;
    }

    h1 {
        font-size: 1.5rem;
        color: #3a3a3a;
        margin: 0;
    }

    .alert-container {
        position: relative;
        display: flex;
        flex-direction: column;
        --button-height: 40px;

        button {
            height: var(--button-height);
        }

        .alert-dropdown {
            position: absolute;
            transform: translate(0px, var(--button-height));
            right: 0;
            z-index: 1000;
            display: flex;
            flex-direction: column;

            border: 1px solid black;
            padding: 0;
            list-style: none;
            margin: 0;
            min-width: 250px;
            min-height: 150px;
            max-height: 300px;
            overflow-y: auto;
            background-color: white;

            &:not(.opened) {
                display: none !important;
            }
        }

        .alert-item {
            display: block;
            padding: 5px 10px;
            background-color: white;

            &.alert-item--link {
                padding: 0;
            }

            &.alert-item--link a {
                display: block;
                padding: 5px 10px;
                text-decoration: none;
                color: black;
                width: 100%;
            }

            &:hover {
                background-color: #eee;
            }
        }
    }
}

.homepage {
    .app-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: 1rem;
        text-decoration: none;

        .icon-wrapper {
            width: 150px;
            height: 150px;

            &:not(.icon-wrapper--full) {
                background: #eaeaea;
                border-radius: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 35px;
            }

            img {
                max-width: 100%;
                max-height: 100%;
            }
        }

        span {
            margin-top: 10px;
            color: #3a3a3a;
        }
    }
}
