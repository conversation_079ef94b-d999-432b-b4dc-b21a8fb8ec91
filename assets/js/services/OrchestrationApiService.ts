export const apiEndpoints = {
    invokeChat: {
        type: 'standard',
    },
    transcript: {
        type: 'standard',
    },
    invokeStream: {
        type: 'stream',
    },
    invokeUpload: {
        type: 'upload',
    },
    createRAG: {
        type: 'void',
    },
};

export type InvokeUploadResponse = {
    path: string;
};

/**
 * Service pour interagir avec l'API d'orchestration
 */
export default class OrchestrationApiService {
    static async call(apiCall: any, assistantId: string, assistantParams: any) {
        const method: string | null = OrchestrationApiService?.[apiCall.endpoint] ?? null;
        if (!method) {
            throw new Error(`Méthode d'appel à l'API non trouvée: ${apiCall.endpoint}`);
        }

        return await method(assistantId, assistantParams);
    }

    static async streamedCall(
        apiCall: any,
        assistantId: string,
        assistantParams: any,
        onToken: (token: string) => void,
        onComplete: () => void,
        onError: (error: any) => void
    ) {
        const method: string | null = OrchestrationApiService?.[apiCall.endpoint] ?? null;
        if (!method) {
            throw new Error(`Méthode d'appel à l'API non trouvée: ${apiCall.endpoint}`);
        }

        return await method(assistantId, assistantParams, onToken, onComplete, onError);
    }

    /**
     * Appelle l'API d'orchestration en mode non-streaming
     *
     * @param {string} modelId - L'ID du modèle à utiliser
     * @param {string} prompt - Le prompt à envoyer
     * @param {Object} options - Options supplémentaires
     * @returns {Promise<Object>} - La réponse de l'API
     */
    static async invokeChat(modelId, prompt, options = {}) {
        const { temperature = 0, maxTokens = 2048 } = options;

        const response = await fetch('/api/orchestration/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model_id: modelId,
                prompt,
                temperature,
                max_tokens: maxTokens,
            }),
        });

        if (!response.ok) {
            throw new Error(`Erreur API: ${response.status}`);
        }

        return await response.json();
    }

    static async transcript(assistantId: string, assistantParams: any) {
        const response = await fetch(`/api/${assistantId}/transcript`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                publicUri: assistantParams.publicUri,
            }),
        });

        if (!response.ok) {
            throw new Error(`Erreur API: ${response.status}`);
        }

        return await response.json();
    }

    static async createRAG(assistantId: string, assistantParams: any) {
        const response = await fetch(`/api/rag/${assistantId}/create`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                publicUri: assistantParams.publicUri,
            }),
        });

        if (!response.ok) {
            throw new Error(`Erreur API: ${response.status}`);
        }

        return await response.json();
    }

    /**
     * Appelle l'API d'orchestration en mode streaming
     *
     * @param {string} assistantId - L'ID du assistant
     * @param {object} assistantParams - L'ensemble des paramètres de cet assistant
     * @param {function} onToken - Callback appelé pour chaque token reçu
     * @param {function} onComplete - Callback appelé à la fin du streaming
     * @param {function} onError - Callback appelé en cas d'erreur
     */
    static async invokeStream(
        assistantId: string,
        assistantParams: any,
        onToken: (token: string) => void,
        onComplete: () => void,
        onError: (error: any) => void
    ) {
        try {
            const response = await fetch(`/api/${assistantId}/stream`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    assistantParams,
                }),
            });

            if (!response.ok) {
                throw new Error(`Erreur API: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();

                if (done) {
                    break;
                }

                // Décode les données reçues et les ajoute au buffer
                buffer += decoder.decode(value, { stream: true });

                // Traite les lignes complètes
                const lines = buffer.split('\n');
                buffer = lines.pop(); // Garde la dernière ligne incomplète dans le buffer

                for (const line of lines) {
                    if (line.trim() === '') continue;

                    try {
                        const event = JSON.parse(line);

                        if (event.status === 'token' && event.token) {
                            onToken(event.token);
                        } else if (event.status === 'end' && event.trace) {
                            onComplete(event.trace);
                            return;
                        } else if (event.status === 'error') {
                            onError(new Error(event.message || 'Erreur inconnue'));
                            return;
                        }
                    } catch (e) {
                        console.error('Erreur de parsing JSON:', e, line);
                    }
                }
            }

            // Si on arrive ici sans avoir reçu d'événement 'end', on appelle quand même onComplete
            onComplete({ output: '' });
        } catch (error) {
            onError(error);
        }
    }

    static async invokeUpload(assistantId: string, file: File): Promise<InvokeUploadResponse> {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch(`/api/${assistantId}/file-upload`, {
            method: 'POST',
            body: formData,
        });

        if (!response.ok) {
            throw new Error(`Erreur API: ${response.status}`);
        }

        return await response.json();
    }
}
