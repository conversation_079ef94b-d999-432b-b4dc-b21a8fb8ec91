import { Controller } from '@hotwired/stimulus';
import { mount, unmount } from 'svelte';
import type { ComponentProps, Component as ComponentType } from 'svelte';
import Assistant from '~/svelte/controllers/Assistant.svelte';

const components = {
    Assistant: Assistant,
};

export default class extends Controller<Element & { root?: ComponentType }> {
    private app: ComponentType | undefined;
    private props: ComponentProps<NonNullable<typeof this.app>> | undefined;

    declare readonly componentValue: keyof typeof components;
    declare readonly propsValue: Record<string, unknown> | null | undefined;

    static values = {
        component: String,
        props: Object,
        intro: Boolean,
    };

    async connect() {
        this.element.innerHTML = '';

        this.props = this.propsValue ?? undefined;

        this.dispatchEvent('connect');

        const Component = components[this.componentValue] ?? null;

        if (!Component) {
            throw new Error(
                `Component ${this.componentValue} not found. available components are ${Object.keys(components)}`
            );
        }

        await this._destroyIfExists();

        this.app = mount(Component, {
            target: this.element,
            // @ts-ignore
            props: this.props as ComponentProps<NonNullable<typeof this.app>>,
        }) as ComponentType;

        this.element.root = this.app;

        this.dispatchEvent('mount', {
            component: Component,
        });
    }

    async disconnect() {
        await this._destroyIfExists();
        this.dispatchEvent('unmount');
    }

    async _destroyIfExists(): Promise<void> {
        if (this.element.root !== undefined) {
            await unmount(this.element.root);
            delete this.element.root;
        }
    }

    private dispatchEvent(name: string, payload: object = {}) {
        const detail = {
            componentName: this.componentValue,
            props: this.props,
            ...payload,
        };
        this.dispatch(name, { detail, prefix: 'svelte' });
    }
}
