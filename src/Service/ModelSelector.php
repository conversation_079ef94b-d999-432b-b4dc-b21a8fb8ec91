<?php

namespace App\Service;

use App\Config\Model\AssistantConfig;
use App\Service\OrchestrationApi\Dto\Model\SearchModelsRequest;
use App\Service\OrchestrationApi\Dto\Model\SearchModelsResponse;
use App\Service\OrchestrationApi\Dto\Model\SearchModelsResponseItem;
use App\Service\OrchestrationApi\OrchestrationApiClient;
use Psr\Cache\InvalidArgumentException;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\Cache\TagAwareCacheInterface;

readonly class ModelSelector
{
    public const CACHE_TAG = 'models';

    public function __construct(
        private OrchestrationApiClient $client,
        private TagAwareCacheInterface $modelsCache,
    ) {
    }

    /**
     * @throws InvalidArgumentException
     */
    public function retrieveModelForAssistant(AssistantConfig $assistantConfig): string
    {
        // mettre en place du cache sur la réponse contenant la liste de tous les modèles
        $response = $this->fetchModelsFromAPI();

        // filtrer ici par rapport aux critères de l'assistant
        dd($response);

        // dans la brique
        // mettre en place une configuration pour toute la brique (elle sera surchargeable par page/action)
        // un conf de modèle =
        // - interne / externe ou les deux (allowedLocations: internal / external)
        // - liste des catégories autorisées
        // - modèle par défaut

        //        $assistantConfig->
        return 'gpt-4o-mini';
    }

    /**
     * @throws InvalidArgumentException
     */
    public function fetchModelsFromAPI(): SearchModelsResponse
    {
        return $this->modelsCache->get(self::CACHE_TAG, function (ItemInterface $item): SearchModelsResponse {
            $item->tag(self::CACHE_TAG);

            return $this->client->searchModels(new SearchModelsRequest(
                filterLocal: null,
                filterCategory: null,
            ));
        });
    }

    /**
     * @return SearchModelsResponseItem[]
     *
     * @throws InvalidArgumentException
     */
    public function retrieveAuthorizedModels(AssistantConfig $config): array
    {
        $modelSelectionConfiguration = $config->getModelSelectionConfiguration();
        $models = $this->fetchModelsFromAPI()->models;

        $defaultModel = null;
        if (null !== $modelSelectionConfiguration->getDefaultModel()) {
            $defaultModelIndex = array_search($modelSelectionConfiguration->getDefaultModel(), array_column($models, 'id'));
            $defaultModel = false !== $defaultModelIndex ? $models[$defaultModelIndex] : null;
            unset($models[$defaultModelIndex]);
        }

        // filtres les modèles par rapport à leur localisation [internal, external]
        $models = array_filter($models, function (SearchModelsResponseItem $model) use ($modelSelectionConfiguration): bool {
            $allowedLocations = $modelSelectionConfiguration->getAllowedLocations();

            return match ($allowedLocations) {
                [], ModelLocation::cases() => true,
                [ModelLocation::External] => !$model->local,
                [ModelLocation::Internal] => $model->local,
                default => false,
            };
        });

        // filtre les modèles issues de catégories configurées
        $models = array_filter($models, function (SearchModelsResponseItem $model) use ($modelSelectionConfiguration): bool {
            $allowedCategories = $modelSelectionConfiguration->getAllowedCategories();
            $allowedCategoriesValues = array_map(fn (ModelCategory $category) => $category->value, $allowedCategories);
            if (empty($allowedCategoriesValues)) {
                return true;
            }

            return in_array($model->category, $allowedCategoriesValues, true);
        });

        if ($defaultModel instanceof SearchModelsResponseItem) {
            array_unshift($models, $defaultModel);
        }

        return array_values($models);
    }
}
