<?php

namespace App\Service\OrchestrationApi;

use App\Service\OrchestrationApi\Dto\InvokeRequest;
use App\Service\OrchestrationApi\Dto\Model\SearchModelsRequest;
use App\Service\OrchestrationApi\Dto\Model\SearchModelsResponse;
use App\Service\OrchestrationApi\Dto\RAG\CreateRAGRequest;
use App\Service\OrchestrationApi\Dto\RAG\CreateRAGResponse;
use App\Service\OrchestrationApi\Dto\Stream\StreamEvent;
use App\Service\OrchestrationApi\Dto\Stream\StreamEventFactory;
use App\Service\OrchestrationApi\Dto\Stream\TraceResponse;
use App\Service\OrchestrationApi\Dto\Transcript\TranscriptRequest;
use App\Service\OrchestrationApi\Dto\Transcript\TranscriptResponse;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\HttpClient\ChunkInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseStreamInterface;

class OrchestrationApiClient
{
    private const API_BASE_PATH = '/api/v1';
    private const CHAT_ENDPOINT = self::API_BASE_PATH.'/chat';
    private const STREAM_ENDPOINT = self::API_BASE_PATH.'/stream';
    private const TRANSCRIPT_ENDPOINT = self::API_BASE_PATH.'/transcript';
    private const SEARCH_MODELS_ENDPOINT = self::API_BASE_PATH.'/model';
    private const CREATE_RAG_ENDPOINT = self::API_BASE_PATH.'/rag/async';

    private HttpClientInterface $client;

    public function __construct(
        HttpClientInterface $httpClient,
        #[Autowire(env: 'ORCHESTRATION_API_KEY')] private readonly string $apiKey,
        #[Autowire(env: 'ORCHESTRATION_API_URL')] private readonly string $baseUrl,
    ) {
        $this->client = $httpClient->withOptions([
            'base_uri' => $this->baseUrl,
            'headers' => [
                'X-API-Key' => $this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
        ]);
    }

    /**
     * Invoke a chat completion.
     *
     * @param InvokeRequest $request The request data
     *
     * @return TraceResponse The response data
     *
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function chat(InvokeRequest $request): TraceResponse
    {
        $response = $this->client->request('POST', self::CHAT_ENDPOINT, [
            'json' => $request->toArray(),
        ]);

        return TraceResponse::fromArray($response->toArray());
    }

    /**
     * Invoke a streaming chat completion.
     *
     * @param InvokeRequest $request The request data
     *
     * @return ResponseStreamInterface The stream of events
     *
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public function stream(InvokeRequest $request): ResponseStreamInterface
    {
        $response = $this->client->request('POST', self::STREAM_ENDPOINT, [
            'json' => $request->toArray(),
            'headers' => [
                'Accept' => 'application/x-ndjson',
            ],
        ]);

        return $this->client->stream($response);
    }

    /**
     * Process a stream chunk and convert it to a StreamEvent.
     *
     * @param ChunkInterface $chunk The chunk to process
     *
     * @return StreamEvent|null The event, or null if the chunk is not a valid event
     */
    public function processStreamChunk(ChunkInterface $chunk): ?StreamEvent
    {
        if (!$chunk->isLast() && $content = $chunk->getContent()) {
            try {
                return StreamEventFactory::fromJson($content);
            } catch (\InvalidArgumentException $e) {
                // Invalid JSON or unknown event type
                return null;
            }
        }

        return null;
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function transcript(TranscriptRequest $request): TranscriptResponse
    {
        // TODO envoyer uniquement l'URL du bucket une fois que cela aura été implémenté côté API
        $content = file_get_contents($request->publicUri);
        if (false === $content) {
            throw new \RuntimeException('Impossible de télécharger le fichier depuis S3');
        }

        $tmpFile = tempnam(sys_get_temp_dir(), 'upload_').'.mp3';
        file_put_contents($tmpFile, $content);

        $response = $this->client->request('POST', self::TRANSCRIPT_ENDPOINT, [
            'query' => [
                'model_id' => $request->modelId,
                'lang' => $request->lang,
            ],
            'body' => [
                'file' => fopen($tmpFile, 'r'),
            ],
        ]);
        $content = $response->getContent();

        unlink($tmpFile);

        return TranscriptResponse::fromString($content);
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function searchModels(SearchModelsRequest $request): SearchModelsResponse
    {
        $response = $this->client->request('GET', self::SEARCH_MODELS_ENDPOINT, [
            'query' => $request->toArray(),
        ]);

        return SearchModelsResponse::fromArray($response->toArray());
    }

    public function createRAG(CreateRAGRequest $request): CreateRAGResponse
    {
        // TODO envoyer uniquement l'URL du bucket une fois que cela aura été implémenté côté API
        $content = file_get_contents($request->publicUri);
        if (false === $content) {
            throw new \RuntimeException('Impossible de télécharger le fichier depuis S3');
        }

        $tmpFile = tempnam(sys_get_temp_dir(), 'upload_').'.pdf';
        file_put_contents($tmpFile, $content);

        $response = $this->client->request('POST', self::CREATE_RAG_ENDPOINT, [
            'body' => [
                'file' => fopen($tmpFile, 'r'),
            ],
        ]);

        unlink($tmpFile);

        return CreateRAGResponse::fromArray($response->toArray());
    }
}
