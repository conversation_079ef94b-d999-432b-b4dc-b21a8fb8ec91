<?php

namespace App\Service\OrchestrationApi\Dto\Model;

class SearchModelsResponseItem
{
    public function __construct(
        public string $category,
        public string $id,
        public bool $local,
        public string $name,
        public int $priority,
        public string $provider,
    ) {
    }

    public static function fromArray(array $array): self
    {
        return new self(
            category: $array['category'],
            id: $array['id'],
            local: $array['local'],
            name: $array['name'],
            priority: $array['priority'],
            provider: $array['provider'],
        );
    }
}
