<?php

namespace App\Service\OrchestrationApi\Dto\Model;

class SearchModelsResponse
{
    /** @var array<SearchModelsResponseItem> */
    public array $models = [];

    private function __construct(
        array $models,
    ) {
        foreach ($models as $model) {
            $this->models[] = SearchModelsResponseItem::fromArray($model);
        }
    }

    public static function fromArray(array $array): self
    {
        return new self($array);
    }
}
