<?php

namespace App\Service\OrchestrationApi\Dto\Stream;

class TraceResponse
{
    /**
     * @param string|null $id            The Trace ID of the request
     * @param string      $output        The output from the model
     * @param int         $input_tokens  The number of input tokens
     * @param int         $output_tokens The number of output tokens
     * @param float       $time_elapsed  The time elapsed for the invocation
     */
    public function __construct(
        private ?string $id,
        private string $output,
        private int $input_tokens,
        private int $output_tokens,
        private float $time_elapsed,
    ) {
    }

    /**
     * Create a TraceResponse from an array.
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['id'] ?? null,
            $data['output'],
            $data['input_tokens'],
            $data['output_tokens'],
            $data['time_elapsed'],
        );
    }

    /**
     * Get the trace ID.
     */
    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Get the output.
     */
    public function getOutput(): string
    {
        return $this->output;
    }

    /**
     * Get the number of input tokens.
     */
    public function getInputTokens(): int
    {
        return $this->input_tokens;
    }

    /**
     * Get the number of output tokens.
     */
    public function getOutputTokens(): int
    {
        return $this->output_tokens;
    }

    /**
     * Get the time elapsed.
     */
    public function getTimeElapsed(): float
    {
        return $this->time_elapsed;
    }
}
