<?php

namespace App\Service\OrchestrationApi\Dto\Stream;

class EndEvent extends StreamEvent
{
    /**
     * @param TraceResponse           $trace      The trace of the invocation
     * @param string                  $status     The status of the event
     * @param \DateTimeInterface|null $created_at The date and time of the event
     */
    public function __construct(
        private TraceResponse $trace,
        string $status = 'end',
        ?\DateTimeInterface $created_at = null,
    ) {
        parent::__construct($status, $created_at);
    }

    /**
     * Create an EndEvent from an array.
     */
    public static function fromArray(array $data): self
    {
        $created_at = isset($data['created_at'])
            ? new \DateTimeImmutable($data['created_at'])
            : null;

        return new self(
            TraceResponse::fromArray($data['trace']),
            $data['status'] ?? 'end',
            $created_at,
        );
    }

    /**
     * Get the trace.
     */
    public function getTrace(): TraceResponse
    {
        return $this->trace;
    }
}
