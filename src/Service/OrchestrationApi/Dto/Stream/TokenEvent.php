<?php

namespace App\Service\OrchestrationApi\Dto\Stream;

class TokenEvent extends StreamEvent
{
    /**
     * @param string                  $token      The token generated by the model
     * @param string                  $status     The status of the event
     * @param \DateTimeInterface|null $created_at The date and time of the event
     */
    public function __construct(
        private string $token,
        string $status = 'token',
        ?\DateTimeInterface $created_at = null,
    ) {
        parent::__construct($status, $created_at);
    }

    /**
     * Create a TokenEvent from an array.
     */
    public static function fromArray(array $data): self
    {
        $created_at = isset($data['created_at'])
            ? new \DateTimeImmutable($data['created_at'])
            : null;

        return new self(
            $data['token'],
            $data['status'] ?? 'token',
            $created_at,
        );
    }

    /**
     * Get the token.
     */
    public function getToken(): string
    {
        return $this->token;
    }
}
