<?php

namespace App\Service\OrchestrationApi\Dto\Stream;

class ErrorEvent extends StreamEvent
{
    /**
     * @param string                  $id          The Trace ID of the request
     * @param string                  $message     The error message
     * @param int                     $status_code The status code of the error
     * @param string                  $status      The status of the event
     * @param \DateTimeInterface|null $created_at  The date and time of the event
     */
    public function __construct(
        private string $id,
        private string $message,
        private int $status_code,
        string $status = 'error',
        ?\DateTimeInterface $created_at = null,
    ) {
        parent::__construct($status, $created_at);
    }

    /**
     * Create an ErrorEvent from an array.
     */
    public static function fromArray(array $data): self
    {
        $created_at = isset($data['created_at'])
            ? new \DateTimeImmutable($data['created_at'])
            : null;

        return new self(
            $data['id'],
            $data['message'],
            $data['status_code'],
            $data['status'] ?? 'error',
            $created_at,
        );
    }

    /**
     * Get the trace ID.
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * Get the error message.
     */
    public function getMessage(): string
    {
        return $this->message;
    }

    /**
     * Get the status code.
     */
    public function getStatusCode(): int
    {
        return $this->status_code;
    }
}
