<?php

namespace App\Service\OrchestrationApi\Dto\Stream;

class StreamEventFactory
{
    /**
     * Create a stream event from a JSON string.
     */
    public static function fromJson(string $json): StreamEvent
    {
        $data = json_decode($json, true);

        if (!$data) {
            throw new \InvalidArgumentException('Invalid JSON data');
        }

        return self::fromArray($data);
    }

    /**
     * Create a stream event from an array.
     */
    public static function fromArray(array $data): StreamEvent
    {
        $status = $data['status'] ?? null;

        return match ($status) {
            'token', null => TokenEvent::fromArray($data),
            'error' => ErrorEvent::fromArray($data),
            'end' => EndEvent::fromArray($data),
            default => throw new \InvalidArgumentException("Unknown event status: $status"),
        };
    }
}
