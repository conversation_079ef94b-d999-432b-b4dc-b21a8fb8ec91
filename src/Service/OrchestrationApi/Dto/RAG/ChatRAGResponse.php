<?php

namespace App\Service\OrchestrationApi\Dto\RAG;

/**
 * Exemple de réponse :
 * {
 * "id": "cc6821b6-1dce-4cbd-965f-bbdb58a180fc",
 * "user_id": "123e4567-e89b-12d3-a456-426614174000",
 * "output": "Ce document concerne les droits des titulaires de la Carte My Engen en matière de protection et de gestion de leurs données personnelles. Voici un résumé des points clés :\n\n1. **Droits sur les Données Personnelles** :\n   - Les titulaires ont le droit d'accès, de rectification, de limitation, de suppression, et éventuellement de portabilité de leurs données.\n   - Ils peuvent également s'opposer",
 * "input_tokens": 2019,
 * "output_tokens": 396,
 * "time_elapsed": 7.94
 * }
 */
class ChatRAGResponse
{
    public function __construct(
        public string $id,
        public string $userId,
        public string $output,
        public int $inputTokens,
        public int $outputTokens,
        public float $timeElapsed,
    ) {
    }

    public static function fromArray(array $array): self
    {
        return new self(
            id: $array['id'],
            userId: $array['user_id'],
            output: $array['output'],
            inputTokens: $array['input_tokens'],
            outputTokens: $array['output_tokens'],
            timeElapsed: $array['time_elapsed'],
        );
    }
}
