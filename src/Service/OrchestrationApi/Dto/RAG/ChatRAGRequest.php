<?php

namespace App\Service\OrchestrationApi\Dto\RAG;

/**
 * Exemple de requête :
 * {
 * "max_tokens": 100,
 * "model_id": "phi4:14b",
 * "prompt": "{{context}} Que contient ce document ?",
 * "temperature": 0,
 * "user_id": "123e4567-e89b-12d3-a456-426614174000"
 * }
 */
class ChatRAGRequest
{
    public function __construct(
        public string $modelId,
        public string $prompt,
        public float $temperature,
        public int $maxTokens,
        public string $userId,
    ) {
    }

    public static function fromArray(array $array): self
    {
        return new self(
            modelId: $array['model_id'],
            prompt: $array['prompt'],
            temperature: $array['temperature'],
            maxTokens: $array['max_tokens'],
            userId: $array['user_id'],
        );
    }
}
