<?php

namespace App\Service\OrchestrationApi\Dto\RAG;

/*
 * Exemple de réponse :
 * {
      "status": "success",
      "retriever_id": "617d285f-34aa-4d8c-907c-aba24f1d8521"
    }
 */
class CreateRAGResponse
{
    public function __construct(
        public string $status,
        public string $retrieverId,
    ) {
    }

    public static function fromArray(array $array): self
    {
        return new self(
            status: $array['status'],
            retrieverId: $array['retriever_id'],
        );
    }

    public function toArray(): array
    {
        return [
            'status' => $this->status,
            'retriever_id' => $this->retrieverId,
        ];
    }
}
