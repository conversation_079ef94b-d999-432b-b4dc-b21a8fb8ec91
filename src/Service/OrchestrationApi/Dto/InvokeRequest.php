<?php

namespace App\Service\OrchestrationApi\Dto;

use Symfony\Component\Uid\Uuid;

class InvokeRequest
{
    public function __construct(
        private readonly string $model_id,
        private readonly string $prompt,
        private readonly float $temperature = 0.0,
        private readonly int $max_tokens = 2048,
        private ?string $user_id = null,
    ) {
        if (null === $user_id) {
            $this->user_id = Uuid::v7()->toRfc4122();
        }
    }

    public function toArray(): array
    {
        return [
            'user_id' => $this->user_id,
            'model_id' => $this->model_id,
            'prompt' => $this->prompt,
            'temperature' => $this->temperature,
            'max_tokens' => $this->max_tokens,
        ];
    }
}
