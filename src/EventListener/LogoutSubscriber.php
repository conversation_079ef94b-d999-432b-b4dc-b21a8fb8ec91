<?php

namespace App\EventListener;

use App\Security\AuthenticatedUser;
use App\Security\Keycloak\KeycloakAdminApiConsumer;
use App\Security\Keycloak\KeycloakUserApiConsumer;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Security\Http\Event\LogoutEvent;

readonly class LogoutSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private Security $security,
        private KeycloakUserApiConsumer $keycloakUserApiConsumer,
        private KeycloakAdminApiConsumer $keycloakAdminApiConsumer,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [LogoutEvent::class => 'onLogout'];
    }

    public function onLogout(LogoutEvent $event): void
    {
        /** @var AuthenticatedUser $user */
        $user = $this->security->getUser();

        if (null !== $user) {
            $accessToken = $this->keycloakUserApiConsumer->getAdminToken()->accessToken;
            $this->keycloakAdminApiConsumer->logoutUserById($accessToken, $user->getSub());
        }
    }
}
