<?php

namespace App\Prompt;

use App\Config\Model\AssistantConfig;
use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;

readonly class PromptBuilder
{
    public function __construct(
        private Environment $twig,
    ) {
    }

    /**
     * @throws SyntaxError
     * @throws RuntimeError
     * @throws LoaderError
     */
    public function __invoke(AssistantConfig $assistantConfig, array $assistantParams): string
    {
        $promptTemplatePath = $assistantConfig->getPromptTemplatePath();

        return $this->twig->render($promptTemplatePath, $assistantParams);
    }
}
