<?php

namespace App\Config\Model;

class AssistantConfig
{
    /**
     * @param string      $id          Identifiant unique de l'assistant
     * @param string      $title       Titre de l'assistant
     * @param string      $description Description de l'assistant
     * @param array<Page> $pages       Pages de l'assistant
     */
    public function __construct(
        private string $id,
        private string $title,
        private string $description,
        private ModelSelectionConfiguration $modelSelectionConfiguration,
        private array $pages = [],
    ) {
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getModelSelectionConfiguration(): ModelSelectionConfiguration
    {
        return $this->modelSelectionConfiguration;
    }

    /**
     * @return array<Page>
     */
    public function getPages(): array
    {
        return $this->pages;
    }

    public function addPage(Page $page): self
    {
        $this->pages[] = $page;

        return $this;
    }

    /** @param Model[] $retrieveAuthorizedModels */
    public function setAuthorizedModels(array $retrieveAuthorizedModels): void
    {
        $this->modelSelectionConfiguration->setAuthorizedModels($retrieveAuthorizedModels);
    }

    public function getPromptTemplatePath(): string
    {
        return sprintf('%s/prompt.html.twig', $this->id);
    }

    /**
     * Crée une instance à partir d'un tableau de données.
     *
     * @param array $data Les données de configuration
     */
    public static function fromArray(array $data): self
    {
        $pages = [];
        foreach ($data['pages'] ?? [] as $pageData) {
            $pages[] = Page::fromArray($pageData);
        }

        return new self(
            $data['id'],
            $data['title'],
            $data['description'],
            ModelSelectionConfiguration::fromArray($data['modelSelectionConfiguration'] ?? []),
            $pages
        );
    }

    /**
     * Convertit l'objet en tableau.
     */
    public function toArray(): array
    {
        $pages = [];
        foreach ($this->pages as $page) {
            $pages[] = $page->toArray();
        }

        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'modelSelectionConfiguration' => $this->modelSelectionConfiguration->toArray(),
            'pages' => $pages,
        ];
    }
}
