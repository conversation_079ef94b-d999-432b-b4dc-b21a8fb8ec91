<?php

namespace App\Config\Model;

use App\Service\ModelCategory;
use App\Service\ModelLocation;

class ModelSelectionConfiguration
{
    /** @var Model[] */
    private $authorizedModels = [];

    public function __construct(
        /** @var ModelCategory[] $allowedCategories */
        private array $allowedCategories = [],
        /** @var ModelLocation[] $allowedLocations */
        private array $allowedLocations = [],
        private ?string $defaultModel = null,
    ) {
    }

    /**
     * @return ModelCategory[]
     */
    public function getAllowedCategories(): array
    {
        return $this->allowedCategories;
    }

    /**
     * @return ModelLocation[]
     */
    public function getAllowedLocations(): array
    {
        return $this->allowedLocations;
    }

    public function getDefaultModel(): ?string
    {
        return $this->defaultModel;
    }

    /** @param Model[] $retrieveAuthorizedModels */
    public function setAuthorizedModels(array $retrieveAuthorizedModels): void
    {
        $this->authorizedModels = $retrieveAuthorizedModels;
    }

    /** @return Model[] */
    public function getAuthorizedModels(): array
    {
        return $this->authorizedModels;
    }

    public static function fromArray(array $modelSelectionConfiguration): static
    {
        $allowedCategories = [];
        foreach ($modelSelectionConfiguration['allowedCategories'] ?? [] as $category) {
            $modelCategory = ModelCategory::tryFrom($category);
            if ($modelCategory instanceof ModelCategory) {
                $allowedCategories[] = $modelCategory;
            }
        }

        $allowedLocations = [];
        foreach ($modelSelectionConfiguration['allowedLocations'] ?? [] as $location) {
            $modelLocation = ModelLocation::tryFrom($location);
            if ($modelLocation instanceof ModelLocation) {
                $allowedLocations[] = $modelLocation;
            }
        }

        return new self(
            allowedCategories: $allowedCategories,
            allowedLocations: $allowedLocations,
            defaultModel: $modelSelectionConfiguration['defaultModel'] ?? null,
        );
    }

    public function toArray(): array
    {
        return [
            'allowedCategories' => array_map(fn (ModelCategory $category) => $category->value, $this->allowedCategories),
            'allowedLocations' => array_map(fn (ModelLocation $location) => $location->value, $this->allowedLocations),
            'defaultModel' => $this->defaultModel,
            'authorizedModels' => $this->authorizedModels,
        ];
    }

    public function isModelAuthorized(mixed $selectedModelId): bool
    {
        return in_array($selectedModelId, array_column($this->authorizedModels, 'id'), true);
    }
}
