<?php

namespace App\Config\Model;

use App\Service\OrchestrationApi\Dto\Model\SearchModelsResponseItem;

class Model
{
    public function __construct(
        public string $category,
        public string $id,
        public bool $local,
        public string $name,
        public int $priority,
        public string $provider,
    ) {
    }

    public static function fromSearchModelsResponseItem(SearchModelsResponseItem $item): static
    {
        return new self(
            category: $item->category,
            id: $item->id,
            local: $item->local,
            name: $item->name,
            priority: $item->priority,
            provider: $item->provider
        );
    }

    /**
     * @param array<SearchModelsResponseItem> $retrieveAuthorizedModels
     *
     * @return array<Model>
     */
    public static function fromSearchModelsItems(array $retrieveAuthorizedModels): array
    {
        $models = [];
        foreach ($retrieveAuthorizedModels as $model) {
            $models[] = self::fromSearchModelsResponseItem($model);
        }

        return $models;
    }
}
