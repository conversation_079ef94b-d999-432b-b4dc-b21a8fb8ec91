<?php

namespace App\Config\Model;

class Component
{
    /**
     * @param string                $id        Identifiant unique du composant
     * @param string                $component Type de composant (Text, Button, etc.)
     * @param array                 $props     Propriétés du composant
     * @param string|null           $bind      Liaison de données
     * @param array<Component>|null $children  Composants enfants
     * @param Action|null           $action    Action associée au composant
     */
    public function __construct(
        private string $id,
        private string $component,
        private array $props = [],
        private ?string $bind = null,
        private ?array $children = null,
        private ?Action $action = null,
    ) {
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getComponent(): string
    {
        return $this->component;
    }

    public function getProps(): array
    {
        return $this->props;
    }

    public function getBind(): ?string
    {
        return $this->bind;
    }

    /**
     * @return array<Component>|null
     */
    public function getChildren(): ?array
    {
        return $this->children;
    }

    public function getAction(): ?Action
    {
        return $this->action;
    }

    public function addChild(Component $child): self
    {
        if (null === $this->children) {
            $this->children = [];
        }
        $this->children[] = $child;

        return $this;
    }

    public function setAction(Action $action): self
    {
        $this->action = $action;

        return $this;
    }

    /**
     * Crée une instance à partir d'un tableau de données.
     *
     * @param array $data Les données du composant
     */
    public static function fromArray(array $data): self
    {
        $children = null;
        if (isset($data['children'])) {
            $children = [];
            foreach ($data['children'] as $childData) {
                $children[] = self::fromArray($childData);
            }
        }

        $action = isset($data['action']) ? Action::fromArray($data['action']) : null;

        return new self(
            $data['id'],
            $data['component'],
            $data['props'] ?? [],
            $data['bind'] ?? null,
            $children,
            $action
        );
    }

    /**
     * Convertit l'objet en tableau.
     */
    public function toArray(): array
    {
        $result = [
            'id' => $this->id,
            'component' => $this->component,
            'props' => $this->props,
        ];

        if (null !== $this->bind) {
            $result['bind'] = $this->bind;
        }

        if (null !== $this->children) {
            $childrenArray = [];
            foreach ($this->children as $child) {
                $childrenArray[] = $child->toArray();
            }
            $result['children'] = $childrenArray;
        }

        if (null !== $this->action) {
            $result['action'] = $this->action->toArray();
        }

        return $result;
    }
}
