<?php

namespace App\Config\Model;

class Page
{
    /**
     * @param string           $id         Identifiant unique de la page
     * @param string           $title      Titre de la page
     * @param array<Component> $components Composants de la page
     * @param array<Component> $actions    Actions de la page
     * @param array<Api>       $apiCalls   Configuration API de la page
     */
    public function __construct(
        private string $id,
        private string $title,
        private array $components = [],
        private array $actions = [],
        private array $apiCalls = [],
    ) {
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @return array<Component>
     */
    public function getComponents(): array
    {
        return $this->components;
    }

    /**
     * @return array<Component>
     */
    public function getActions(): array
    {
        return $this->actions;
    }

    /**
     * @return array<Api>
     */
    public function getApiCalls(): array
    {
        return $this->apiCalls;
    }

    public function addComponent(Component $component): self
    {
        $this->components[] = $component;

        return $this;
    }

    public function addAction(Component $action): self
    {
        $this->actions[] = $action;

        return $this;
    }

    public function setApi(Api $api): self
    {
        $this->api = $api;

        return $this;
    }

    /**
     * Crée une instance à partir d'un tableau de données.
     *
     * @param array $data Les données de la page
     */
    public static function fromArray(array $data): self
    {
        $components = [];
        foreach ($data['components'] ?? [] as $componentData) {
            $components[] = Component::fromArray($componentData);
        }

        $actions = [];
        foreach ($data['actions'] ?? [] as $actionData) {
            $actions[] = Component::fromArray($actionData);
        }

        $apiCalls = [];
        foreach ($data['apiCalls'] ?? [] as $apiConfigData) {
            $apiCalls[] = Api::fromArray($apiConfigData);
        }

        return new self(
            $data['id'],
            $data['title'],
            $components,
            $actions,
            $apiCalls
        );
    }

    /**
     * Convertit l'objet en tableau.
     */
    public function toArray(): array
    {
        $components = [];
        foreach ($this->components as $component) {
            $components[] = $component->toArray();
        }

        $actions = [];
        foreach ($this->actions as $action) {
            $actions[] = $action->toArray();
        }

        $apiCalls = [];
        foreach ($this->apiCalls as $apiCall) {
            $apiCalls[] = $apiCall->toArray();
        }

        return [
            'id' => $this->id,
            'title' => $this->title,
            'components' => $components,
            'actions' => $actions,
            'apiCalls' => $apiCalls,
        ];
    }
}
