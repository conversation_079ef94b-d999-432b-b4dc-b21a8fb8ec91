<?php

namespace App\Config\Model;

class Action
{
    /**
     * @param string      $type           Type d'action (navigate, submit, copy, export)
     * @param string|null $target         Cible de l'action
     * @param array|null  $setValues      Valeurs à définir
     * @param string|null $successMessage Message de succès
     * @param array|null  $options        Options supplémentaires
     */
    public function __construct(
        private string $type,
        private ?string $apiCallId = null,
        private ?string $target = null,
        private ?array $setValues = null,
        private ?string $successMessage = null,
        private ?array $options = null,
    ) {
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getApiCallId(): ?string
    {
        return $this->apiCallId;
    }

    public function getTarget(): ?string
    {
        return $this->target;
    }

    public function getSetValues(): ?array
    {
        return $this->setValues;
    }

    public function getSuccessMessage(): ?string
    {
        return $this->successMessage;
    }

    public function getOptions(): ?array
    {
        return $this->options;
    }

    /**
     * Crée une instance à partir d'un tableau de données.
     *
     * @param array $data Les données de l'action
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['type'],
            $data['apiCallId'] ?? null,
            $data['target'] ?? null,
            $data['setValues'] ?? null,
            $data['successMessage'] ?? null,
            $data['options'] ?? null
        );
    }

    /**
     * Convertit l'objet en tableau.
     */
    public function toArray(): array
    {
        $result = [
            'type' => $this->type,
        ];

        if (null !== $this->apiCallId) {
            $result['apiCallId'] = $this->apiCallId;
        }

        if (null !== $this->target) {
            $result['target'] = $this->target;
        }

        if (null !== $this->setValues) {
            $result['setValues'] = $this->setValues;
        }

        if (null !== $this->successMessage) {
            $result['successMessage'] = $this->successMessage;
        }

        if (null !== $this->options) {
            $result['options'] = $this->options;
        }

        return $result;
    }
}
