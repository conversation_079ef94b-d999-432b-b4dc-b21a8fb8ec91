<?php

namespace App\Config;

use App\Config\Model\AssistantConfig;
use App\Config\Model\Model;
use App\Service\ModelSelector;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Finder\Finder;

class ConfigLoader
{
    private const CONFIG_DIR = 'config/assistants/config';

    /**
     * @param string $projectDir Répertoire racine du projet
     */
    public function __construct(
        #[Autowire(param: 'kernel.project_dir')] private string $projectDir,
        private ModelSelector $modelSelector,
    ) {
    }

    public function loadAssistantConfig(string $assistantId): AssistantConfig
    {
        $configPath = $this->getConfigPath($assistantId);

        if (!file_exists($configPath)) {
            throw new \InvalidArgumentException(sprintf('Assistant configuration not found for ID "%s"', $assistantId));
        }

        $config = $this->loadAssistantConfigFromFile($configPath);
        $config->setAuthorizedModels(
            Model::fromSearchModelsItems($this->modelSelector->retrieveAuthorizedModels($config))
        );

        return $config;
    }

    public function loadAssistantConfigFromFile(string $filePath): AssistantConfig
    {
        if (!file_exists($filePath)) {
            throw new \InvalidArgumentException(sprintf('Configuration file not found: "%s"', $filePath));
        }

        $jsonContent = file_get_contents($filePath);
        if (false === $jsonContent) {
            throw new \InvalidArgumentException(sprintf('Failed to read configuration file: "%s"', $filePath));
        }

        try {
            $data = json_decode($jsonContent, true, 512, JSON_THROW_ON_ERROR);

            return AssistantConfig::fromArray($data);
        } catch (\JsonException $e) {
            throw new \InvalidArgumentException(sprintf('Invalid JSON in configuration file: "%s"', $filePath), 0, $e);
        } catch (\Throwable $e) {
            throw new \InvalidArgumentException(sprintf('Failed to parse configuration file: "%s"', $filePath), 0, $e);
        }
    }

    public function getAvailableAssistants(): array
    {
        $configDir = $this->getConfigDir();
        $assistants = [];

        if (!is_dir($configDir)) {
            return $assistants;
        }

        $finder = new Finder();
        $finder->directories()->in($configDir);

        foreach ($finder as $directory) {
            try {
                $config = $this->loadAssistantConfigFromFile($this->getConfigPath($directory->getFilename()));
                $assistants[$config->getId()] = $config->getTitle();
            } catch (\Throwable $e) {
                // Skip invalid configurations
                continue;
            }
        }

        return $assistants;
    }

    /**
     * Retourne le chemin vers le répertoire de configuration des assistants.
     */
    private function getConfigDir(): string
    {
        return $this->projectDir.'/'.self::CONFIG_DIR;
    }

    /**
     * Retourne le chemin vers le fichier de configuration d'un assistant.
     */
    private function getConfigPath(string $assistantId): string
    {
        return $this->getConfigDir().'/'.$assistantId.'/assistant.json';
    }
}
