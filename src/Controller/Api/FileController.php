<?php

namespace App\Controller\Api;

use App\Config\Model\AssistantConfig;
use Aws\S3\S3Client;
use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapUploadedFile;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Uid\Uuid;

#[Route('/api', name: 'app_api_')]
class FileController extends AbstractController
{
    #[Route('/{assistantId}/file-upload', name: 'file_upload', methods: ['POST'])]
    public function fileUpload(
        AssistantConfig $assistantConfig,
        #[MapUploadedFile] UploadedFile $file,
        FilesystemOperator $bucketStorage,
        S3Client $s3Client,
        #[Autowire(env: 'MINIO_BUCKET_NAME')] string $bucketName,
    ) {
        // TODO mettre en place un service charger de faire des assertions sur le fichier
        // en fonction de la config de l'assistant

        $targetPath = Uuid::v7()->toRfc4122().'.'.$file->getClientOriginalExtension();
        try {
            $bucketStorage->write($targetPath, $file->getContent());
        } catch (FilesystemException $e) {
            return new JsonResponse(['error' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        $bucketStorage->publicUrl($targetPath);
        $publicUri = $s3Client->createPresignedRequest($s3Client->getCommand('GetObject', [
            'Bucket' => $bucketName,
            'Key' => $targetPath,
        ]), '+10 minutes')->getUri();

        return new JsonResponse([
            'path' => $publicUri,
        ]);
    }
}
