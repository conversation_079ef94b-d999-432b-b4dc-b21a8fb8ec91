<?php

namespace App\Controller\Api;

use App\Config\Model\AssistantConfig;
use App\Controller\DTO\StreamRagRouteParams;
use App\Prompt\PromptBuilder;
use App\Service\OrchestrationApi\Dto\InvokeRequest;
use App\Service\OrchestrationApi\Dto\RAG\CreateRAGRequest;
use App\Service\OrchestrationApi\Dto\RAG\StreamRAGRequest;
use App\Service\OrchestrationApi\Dto\Stream\StreamRequest;
use App\Service\OrchestrationApi\Dto\Stream\TokenEvent;
use App\Service\OrchestrationApi\OrchestrationApiClient;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/rag', name: 'app_api_rag_')]
class RagController extends AbstractController
{
    public function __construct(
        private readonly OrchestrationApiClient $orchestrationApiClient,
    ) {
    }

    #[Route('/{assistantId}/create', name: 'create', methods: ['POST'])]
    public function create(
        AssistantConfig $assistantConfig,
        #[MapRequestPayload] CreateRAGRequest $requestDTO,
    ): JsonResponse {
        try {
            $response = $this->orchestrationApiClient->createRAG($requestDTO);
            return $this->json($response->toArray());
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/{assistantId}/stream', name: 'stream', methods: ['POST'])]
    public function streamRAG(
        AssistantConfig $assistantConfig,
        #[MapRequestPayload] StreamRagRouteParams $requestParams,
        PromptBuilder $promptBuilder,
    ): StreamedResponse {
        $selectedModelId = $requestParams->modelId;
        if (null === $selectedModelId) {
            $selectedModelId = $assistantConfig->getModelSelectionConfiguration()->getDefaultModel();
        }
        if (null !== $selectedModelId && !$assistantConfig->getModelSelectionConfiguration()->isModelAuthorized($selectedModelId)) {
            throw new \InvalidArgumentException('Model ID is not authorized');
        }

        $apiRequest = new StreamRAGRequest(
            modelId: $selectedModelId,
            prompt: $promptBuilder->__invoke($assistantConfig, $requestParams->promptParams),
            temperature: 0.0,
            maxTokens: 2048,
            userId: $this->getUser()->getUserIdentifier(),
        );

        return new StreamedResponse(function () use ($apiRequest, $requestParams) {
            try {
                $stream = $this->orchestrationApiClient->streamRAG($apiRequest, $requestParams->retrieverId);

                foreach ($stream as $chunk) {
                    $event = $this->orchestrationApiClient->processStreamChunk($chunk);

                    if (null !== $event) {
                        if ($event instanceof TokenEvent) {
                            echo json_encode([
                                'status' => $event->getStatus(),
                                'created_at' => $event->getCreatedAt()->format(\DateTimeInterface::ATOM),
                                'token' => $event->getToken(),
                            ])."\n";
                        } else {
                            // For other event types, we'd need similar handling
                            echo json_encode([
                                'status' => $event->getStatus(),
                                'created_at' => $event->getCreatedAt()->format(\DateTimeInterface::ATOM),
                                // Add other properties based on event type
                            ])."\n";
                        }
                        flush();
                    }
                }
            } catch (\Exception $e) {
                echo json_encode(['error' => $e->getMessage()])."\n";
                flush();
            }
        }, Response::HTTP_OK, [
            'Content-Type' => 'application/x-ndjson',
            'Cache-Control' => 'no-cache',
            'X-Accel-Buffering' => 'no',
        ]);
    }
}
