<?php

namespace App\Controller\ValueResolver;

use App\Config\ConfigLoader;
use App\Config\Model\AssistantConfig;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;

class AssistantValueResolver implements ValueResolverInterface
{
    public function __construct(
        private readonly ConfigLoader $configLoader,
    ) {
    }

    public function resolve(Request $request, ArgumentMetadata $argument): iterable
    {
        $argumentType = $argument->getType();
        if (
            !$argumentType
            || !is_a($argumentType, AssistantConfig::class, true)
        ) {
            return [];
        }

        $assistantId = $request->attributes->getString('assistantId');

        if (!is_string($assistantId)) {
            return [];
        }

        $assistantConfig = $this->configLoader->loadAssistantConfig($assistantId);

        return [
            $assistantConfig,
        ];
    }
}
