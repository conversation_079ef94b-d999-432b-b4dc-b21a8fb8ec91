<?php

namespace App\Controller;

use App\Config\ConfigLoader;
use App\Config\Model\AssistantConfig;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\SerializerInterface;

class AssistantController extends AbstractController
{
    #[Route('/assistants/{assistantId}', name: 'app_assistant_show')]
    public function show(AssistantConfig $assistantConfig, SerializerInterface $serializer): Response
    {
        try {
            return $this->render('assistant/show.html.twig', [
                'formSchema' => $serializer->normalize($assistantConfig),
                'assistantId' => $assistantConfig->getId(),
            ]);
        } catch (\InvalidArgumentException $e) {
            throw $this->createNotFoundException('Assistant not found');
        }
    }
}
