<?php

namespace App\Security\Keycloak\DTO;

class GetTokenResponse
{
    private function __construct(
        public string $accessToken,
        public int $expiresIn,
        public int $refreshExpiresIn,
        public string $refreshToken,
        public string $tokenType,
        public string $idToken,
        public int $notBeforePolicy,
        public string $sessionState,
        public string $scope,
    ) {
    }

    public static function fromArray(array $array): self
    {
        return new self(
            accessToken: $array['access_token'],
            expiresIn: $array['expires_in'],
            refreshExpiresIn: $array['refresh_expires_in'],
            refreshToken: $array['refresh_token'],
            tokenType: $array['token_type'],
            idToken: $array['id_token'],
            notBeforePolicy: $array['not-before-policy'],
            sessionState: $array['session_state'],
            scope: $array['scope'],
        );
    }
}
