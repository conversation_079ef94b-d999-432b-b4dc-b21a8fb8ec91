<?php

namespace App\Security\Keycloak;

use App\Security\Keycloak\DTO\GetAdminTokenResponse;
use App\Security\Keycloak\DTO\GetRefreshTokenResponse;
use App\Security\Keycloak\DTO\GetTokenResponse;
use App\Security\Keycloak\DTO\GetUserInfoResponse;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class KeycloakUserApiConsumer
{
    public const REFRESH_TOKEN_ENDPOINT = '/token';
    public const ACCESS_TOKEN_ENDPOINT = '/token';
    public const USERINFO_ENDPOINT = '/userinfo';

    public function __construct(
        #[Autowire(env: 'KEYCLOAK_HOSTNAME')] private string $hostname,
        #[Autowire(env: 'KEYCLOAK_REALM')] private string $realm,
        #[Autowire(env: 'KEYCLOAK_CLIENT_ID')] private string $clientId,
        #[Autowire(env: 'KEYCLOAK_ADMIN_USERNAME')] private string $adminUsername,
        #[Autowire(env: 'KEYCLOAK_ADMIN_PASSWORD')] private string $adminPassword,
        private readonly HttpClientInterface $httpClient,
        private readonly RouterInterface $router,
    ) {
    }

    private function buildUrl(string $endpoint): string
    {
        return \sprintf(
            '%s/realms/%s/protocol/openid-connect%s',
            $this->hostname,
            $this->realm,
            $endpoint
        );
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function getUserInfo(string $accessToken): GetUserInfoResponse
    {
        $response = $this->httpClient->request('GET', $this->buildUrl(self::USERINFO_ENDPOINT), [
            'headers' => ['Authorization' => 'Bearer '.$accessToken],
        ]);

        return GetUserInfoResponse::fromArray($response->toArray(), $this->clientId);
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function getToken(string $code): GetTokenResponse
    {
        $redirectUri = $this->router->generate('app_login_callback', [], UrlGeneratorInterface::ABSOLUTE_URL);
        $response = $this->httpClient->request('POST', $this->buildUrl(self::ACCESS_TOKEN_ENDPOINT), [
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'body' => [
                'grant_type' => 'authorization_code',
                'code' => $code,
                'redirect_uri' => $redirectUri,
                'client_id' => $this->clientId,
            ],
        ]);

        return GetTokenResponse::fromArray($response->toArray());
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function getAdminToken(): GetAdminTokenResponse
    {
        $response = $this->httpClient->request('POST', $this->buildUrl(self::ACCESS_TOKEN_ENDPOINT), [
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'body' => [
                'grant_type' => 'password',
                'client_id' => $this->clientId,
                'username' => $this->adminUsername,
                'password' => $this->adminPassword,
            ],
        ]);

        return GetAdminTokenResponse::fromArray($response->toArray());
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function getRefreshToken(string $refreshToken): GetRefreshTokenResponse
    {
        $response = $this->httpClient->request('POST', $this->buildUrl(self::REFRESH_TOKEN_ENDPOINT), [
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'body' => [
                'grant_type' => 'refresh_token',
                'refresh_token' => $refreshToken,
                'client_id' => $this->clientId,
            ],
        ]);

        return GetRefreshTokenResponse::fromArray($response->toArray());
    }
}
