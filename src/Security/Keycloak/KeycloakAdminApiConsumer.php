<?php

namespace App\Security\Keycloak;

use App\Security\Keycloak\DTO\GetUserResponse;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class KeycloakAdminApiConsumer
{
    public const REGISTER_USER_ENDPOINT = '/users';
    public const VERIFY_EMAIL_AVAILABILITY_ENDPOINT = '/users?email=:email';
    public const LOGOUT_USER_ENDPOINT = '/users/:id/logout';

    public function __construct(
        #[Autowire(env: 'KEYCLOAK_HOSTNAME')] private string $hostname,
        #[Autowire(env: 'KEYCLOAK_REALM')] private string $realm,
        private readonly HttpClientInterface $httpClient,
    ) {
    }

    private function buildUrl(string $endpoint): string
    {
        return sprintf(
            '%s/admin/realms/%s%s',
            $this->hostname,
            $this->realm,
            $endpoint
        );
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function getUserByEmail(string $accessToken, string $email): ?GetUserResponse
    {
        $url = str_replace(':email', $email, $this->buildUrl(self::VERIFY_EMAIL_AVAILABILITY_ENDPOINT));
        $response = $this->httpClient->request('GET', $url, [
            'headers' => [
                'Authorization' => 'Bearer '.$accessToken,
            ],
        ]);
        $data = $response->toArray();

        return [] === $data ? null : GetUserResponse::fromArray($data);
    }

    //    public function registerUser(string $accessToken, RegistrationDTO $registrationDTO): RegisterUserResponse
    //    {
    //        $response = $this->httpClient->request('POST', $this->buildUrl(self::REGISTER_USER_ENDPOINT), [
    //            'headers' => [
    //                'Authorization' => 'Bearer ' . $accessToken,
    //            ],
    //            'json' => [
    //                'username' => $registrationDTO->email,
    //                'email' => $registrationDTO->email,
    //                'enabled' => true,
    //                'firstName' => $registrationDTO->firstname,
    //                'lastName' => $registrationDTO->lastname,
    //                'requiredActions' => ['VERIFY_EMAIL'],
    //            ],
    //        ]);
    //
    //        // il est possible de retouver l'id de l'utilisateur dans l'en-tête Location directement
    //        $headers = $response->getHeaders();
    //        if (null === $headers['location'] || empty($headers['location'])) {
    //            // ce n'est pas implémenté ici mais il est également possible de trouver cet id
    //            // en appelant la route d'API suivante : https://<host>/admin/realms/<realm>/users?username=nouvel_utilisateur
    //            throw new \LogicException('Aucune réponse Location');
    //        }
    //        $userUri = $headers['location'][0];
    //
    //        return new RegisterUserResponse(userUri: $userUri);
    //    }

    public function setUserPassword(string $accessToken, string $userUri, string $password): void
    {
        $response = $this->httpClient->request('PUT', $userUri.'/reset-password', [
            'headers' => [
                'Authorization' => 'Bearer '.$accessToken,
            ],
            'json' => [
                'type' => 'password',
                'value' => $password,
                'temporary' => false,
            ],
        ]);
    }

    /**
     * @throws TransportExceptionInterface
     */
    public function logoutUserById(string $accessToken, string $sub): void
    {
        $url = str_replace(':id', $sub, $this->buildUrl(self::LOGOUT_USER_ENDPOINT));
        $this->httpClient->request('POST', $url, [
            'headers' => [
                'Authorization' => 'Bearer '.$accessToken,
            ],
        ]);
    }
}
