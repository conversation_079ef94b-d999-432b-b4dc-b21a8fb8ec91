<?php

namespace App\Security\Keycloak;

use App\Security\Keycloak\DTO\GetRefreshTokenResponse;
use App\Security\Keycloak\DTO\GetTokenResponse;
use Symfony\Component\Clock\DatePoint;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

readonly class KeycloakUserAuthenticator
{
    public function __construct(
        private KeycloakUserApiConsumer $keycloakApiConsumer,
        private RequestStack $requestStack,
    ) {
    }

    public function authenticate(string $sub): void
    {
        // récupération du token d'accès à l'API keycloak pour l'utilisateur
        try {
            $response = $this->keycloakApiConsumer->getToken($sub);
        } catch (ClientExceptionInterface|DecodingExceptionInterface|RedirectionExceptionInterface|TransportExceptionInterface|ServerExceptionInterface $e) {
            throw new AuthenticationException($e->getMessage(), 0, $e);
        }

        // sauvegarde des informations liées au token dans la session
        $this->saveTokenInfos($response);
    }

    public function refreshConnection(): void
    {
        $session = $this->requestStack->getSession();

        $accessTokenExpirationDate = $session->get('accessTokenExpirationDate');
        if (new DatePoint() < $accessTokenExpirationDate) {
            return;
        }

        $refreshTokenExpirationDate = $session->get('refreshTokenExpirationDate');
        if (new DatePoint() > $refreshTokenExpirationDate) {
            throw new AuthenticationException('Session expired');
        }

        try {
            $response = $this->keycloakApiConsumer->getRefreshToken($session->get('refreshToken'));
        } catch (ClientExceptionInterface|DecodingExceptionInterface|RedirectionExceptionInterface|TransportExceptionInterface|ServerExceptionInterface $e) {
            throw new AuthenticationException($e->getMessage(), 0, $e);
        }

        // sauvegarde des informations liées au token dans la session
        $this->saveTokenInfos($response);
    }

    private function saveTokenInfos(GetTokenResponse|GetRefreshTokenResponse $response): void
    {
        $session = $this->requestStack->getSession();
        $session->set('accessToken', $response->accessToken);
        $session->set('accessTokenExpirationDate', (new DatePoint())->add(new \DateInterval('PT'.$response->expiresIn.'S')));
        $session->set('refreshToken', $response->refreshToken);
        $session->set('refreshTokenExpirationDate', (new DatePoint())->add(new \DateInterval('PT'.$response->refreshExpiresIn.'S')));
    }
}
