<?php

namespace App\Security;

use App\Repository\LocalUserRepository;
use App\Security\Keycloak\KeycloakUserApiConsumer;
use App\Security\Keycloak\KeycloakUserAuthenticator;
use Symfony\Component\Clock\DatePoint;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

readonly class UserProvider implements UserProviderInterface
{
    public function __construct(
        private KeycloakUserApiConsumer $keycloakApiConsumer,
        private RequestStack $requestStack,
        private KeycloakUserAuthenticator $keycloakAuthenticator,
        private LocalUserRepository $userRepository,
    ) {
    }

    public function supportsClass(string $class): bool
    {
        return AuthenticatedUser::class === $class;
    }

    public function loadUserByIdentifier(string $identifier): UserInterface
    {
        $session = $this->requestStack->getSession();

        try {
            // récupération des données de l'utilisateur
            $response = $this->keycloakApiConsumer->getUserInfo($session->get('accessToken'));
        } catch (TransportExceptionInterface|ClientExceptionInterface|RedirectionExceptionInterface|ServerExceptionInterface|DecodingExceptionInterface $e) {
            throw new AuthenticationException($e->getMessage(), 0, $e);
        }

        // sauvegarde de de la date de dernière mise à jour de l'utilisateur dans la session
        $session->set('userLastUpdate', new DatePoint());

        // instanciation de l'utilisateur OIDC
        $oidcUser = $response->toOidcUser();

        // recherche de l'utilisateur correspondant dans la base de données
        $user = $this->userRepository->findOneBy(['ssoIdentifier' => $oidcUser->getSub()]);

        return AuthenticatedUser::fromOidcUserAndLocalUser($oidcUser, $user);
    }

    public function refreshUser(UserInterface $user): UserInterface
    {
        if (!$user instanceof AuthenticatedUser) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', get_class($user)));
        }

        // On vérifie que le access token n'a pas expiré et on le renouvelle si nécessaire
        $this->keycloakAuthenticator->refreshConnection();

        // refresh des informations de l'utilisateur toutes les 1 minutes
        $session = $this->requestStack->getSession();
        $lastUpdateDate = $session->get('userLastUpdate');
        $nextUserInfoRequestDate = $lastUpdateDate?->add(new \DateInterval('PT1M')) ?? null;
        if (null === $nextUserInfoRequestDate || $nextUserInfoRequestDate < new DatePoint()) {
            $user = $this->loadUserByIdentifier($user->getSub());
        }

        return $user;
    }
}
