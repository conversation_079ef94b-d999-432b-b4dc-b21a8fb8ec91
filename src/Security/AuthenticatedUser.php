<?php

namespace App\Security;

use App\Entity\LocalUser;
use Symfony\Component\Security\Core\User\OidcUser;

class AuthenticatedUser extends OidcUser
{
    private ?LocalUser $localUser;

    public static function fromOidcUserAndLocalUser(OidcUser $oidcUser, ?LocalUser $localUser): self
    {
        $authenticatedUser = new self(
            roles: $oidcUser->getRoles(),
            sub: $oidcUser->getSub(),
            name: $oidcUser->getName(),
            givenName: $oidcUser->getGivenName(),
            familyName: $oidcUser->getFamilyName(),
            preferredUsername: $oidcUser->getPreferredUsername(),
            email: $oidcUser->getEmail(),
            emailVerified: $oidcUser->getEmailVerified(),
        );
        $authenticatedUser->localUser = $localUser;

        return $authenticatedUser;
    }

    public function getLocalUser(): LocalUser
    {
        return $this->localUser;
    }

    public function updateLocalUser(LocalUser $localUser): void
    {
        $this->localUser = $localUser;
    }

    public function isRegistrationCompleted(): bool
    {
        return null !== $this->localUser;
    }

    public function hasRole(string $role): bool
    {
        return in_array($role, $this->getRoles(), true);
    }
}
