# Configuration du projet
sonar.projectKey=portail-tools-ia
sonar.projectName=Portail & Tools IA
sonar.links.homepage=https://gitlab.alienor.net/dev-ia/portail-tools-ia
sonar.links.ci=https://gitlab.alienor.net/dev-ia/portail-tools-ia/-/pipelines

# Configuration des repertoires
sonar.sources=src
sonar.tests=tests
sonar.exclusions=src/Kernel.php, src/DataFixtures/**, tests/bootstrap.php

# Configuration ne pas modifier
sonar.sourceEncoding=UTF-8
sonar.qualitygate.wait=true
sonar.php.coverage.reportPaths=coverage.xml
