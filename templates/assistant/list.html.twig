{% extends 'base.html.twig' %}

{% block title %}Assistants disponibles{% endblock %}

{% block body %}
    <div class="container">
        <h1>Assistants disponibles</h1>

        {% if assistants is empty %}
            <p>Aucun assistant disponible.</p>
        {% else %}
            <ul>
                {% for id, title in assistants %}
                    <li>
                        <a href="{{ path('app_assistant_show', {assistantId: id}) }}">{{ title }}</a>
                    </li>
                {% endfor %}
            </ul>
        {% endif %}
    </div>
{% endblock %}
