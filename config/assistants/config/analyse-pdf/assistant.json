{"id": "analyse-pdf", "title": "Analyse de PDF", "description": "Assistant permettant de poser des question à partir d'un document PDF", "modelSelectionConfiguration": {"allowedCategories": ["normal"], "allowedLocations": ["internal"], "defaultModel": "gpt-4o"}, "pages": [{"id": "page-upload-file", "title": "Dépôt du fichier PDF", "components": [{"id": "file-uploader", "component": "FileUpload", "props": {"id": "file", "label": "Fichier à analyser", "required": true, "dropzoneText": "Déposez votre fichier ici ou cliquez pour parcourir"}, "bind": "file"}], "actions": [{"id": "submit-button", "component": "<PERSON><PERSON>", "props": {"label": "Analy<PERSON> le fichier", "variant": "primary"}, "action": {"type": "submit", "apiCallId": "analyse-file", "target": "page-chat"}}], "apiCalls": [{"id": "analyse-file", "endpoint": "createRAG", "method": "POST", "requestMapping": {"inputType": "input-type", "publicUri": ["file"]}, "responseMapping": {"text": "generated-report"}}]}, {"id": "page-chat", "title": "Posez vos questions à propos du contenu du document", "components": [{"id": "report-display", "component": "Cha<PERSON>", "props": {"id": "chat", "label": "Réponse", "readonly": true, "rows": 15}, "bind": "chat"}], "apiCalls": [{"id": "ask-question-about-document", "endpoint": "invokeStream", "method": "POST", "requestMapping": {"content": ["chat"]}, "responseMapping": {"report": "chat"}}]}]}