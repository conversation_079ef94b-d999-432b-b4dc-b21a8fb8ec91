import {apiEndpoints} from "../../../assets/js/services/OrchestrationApiService";

export default {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "type": "object",
    "required": ["id", "title", "description", "pages"],
    "properties": {
        "id": { "type": "string" },
        "title": { "type": "string" },
        "description": { "type": "string" },
        "pages": {
            "type": "array",
            "items": {
                "type": "object",
                "required": ["id", "title", "components"],
                "properties": {
                    "id": { "type": "string" },
                    "title": { "type": "string" },
                    "components": {
                        "type": "array",
                        "items": { "$ref": "#/definitions/component" }
                    },
                    "actions": {
                        "type": "array",
                        "items": { "$ref": "#/definitions/action-component" }
                    },
                    "apiCalls": {
                        "type": "array",
                        "items": {"$ref": "#/definitions/apiCall"},
                    }
                }
            }
        }
    },
    "definitions": {
        "component": {
            "type": "object",
            "required": ["id", "component", "props"],
            "properties": {
                "id": { "type": "string" },
                "component": {
                    "type": "string",
                    "enum": ["Text", "Button", "ButtonGroup", "RichTextarea", "Select", "FileUpload", "ModelSelector"]
                },
                "props": { "type": "object" },
                "bind": { "type": "string" },
                "children": {
                    "type": "array",
                    "items": { "$ref": "#/definitions/component" }
                },
                "action": { "$ref": "#/definitions/action" }
            }
        },
        "action-component": {
            "type": "object",
            "required": ["id", "component", "props"],
            "properties": {
                "id": { "type": "string" },
                "component": {
                    "type": "string",
                    "enum": ["Button"]
                },
                "props": { "type": "object" },
                "action": { "$ref": "#/definitions/action" }
            }
        },
        "action": {
            "type": "object",
            "required": ["type"],
            "properties": {
                "type": {
                    "type": "string",
                    "enum": ["navigate", "submit", "copy", "export"]
                },
                "target": { "type": "string" },
                "setValues": { "type": "object" },
                "successMessage": { "type": "string" },
                "options": { "type": "object" }
            }
        },
        "apiCall": {
            "type": "object",
            "required": ["id", "endpoint", "method", "requestMapping"],
            "properties": {
                "id": {"type": "string"},
                "endpoint": {"type": "string", "enum": Object.keys(apiEndpoints)},
                "method": {"type": "string", "enum": ["GET", "POST", "PUT", "DELETE"]},
                "requestMapping": {"type": "object"},
                "responseMapping": {"type": "object"}
            }
        }
    }
}
