{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["id", "title", "description", "pages"], "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "pages": {"type": "array", "items": {"type": "object", "required": ["id", "title", "components"], "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "components": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "actions": {"type": "array", "items": {"$ref": "#/definitions/action-component"}}, "api": {"$ref": "#/definitions/api"}}}}}, "definitions": {"component": {"type": "object", "required": ["id", "component", "props"], "properties": {"id": {"type": "string"}, "component": {"type": "string", "enum": ["Text", "<PERSON><PERSON>", "ButtonGroup", "RichTextarea", "Select", "FileUpload"]}, "props": {"type": "object"}, "bind": {"type": "string"}, "children": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "action": {"$ref": "#/definitions/action"}}}, "action-component": {"type": "object", "required": ["id", "component", "props"], "properties": {"id": {"type": "string"}, "component": {"type": "string", "enum": ["<PERSON><PERSON>"]}, "props": {"type": "object"}, "action": {"$ref": "#/definitions/action"}}}, "action": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["navigate", "submit", "copy", "export"]}, "target": {"type": "string"}, "setValues": {"type": "object"}, "successMessage": {"type": "string"}, "options": {"type": "object"}}}, "api": {"type": "object", "required": ["endpoint", "method", "requestMapping"], "properties": {"endpoint": {"type": "string"}, "method": {"type": "string", "enum": ["GET", "POST", "PUT", "DELETE"]}, "requestMapping": {"type": "object"}, "responseMapping": {"type": "object"}}}}}