# Read the documentation at https://github.com/thephpleague/flysystem-bundle/blob/master/docs/1-getting-started.md
flysystem:
    storages:
        default.storage:
            adapter: 'local'
            options:
                directory: '%kernel.project_dir%/var/storage/default'
        bucket.storage:
            adapter: 'aws'
            options:
                client: Aws\S3\S3Client
                bucket: '%env(MINIO_BUCKET_NAME)%'
