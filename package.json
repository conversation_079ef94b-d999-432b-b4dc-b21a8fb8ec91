{"type": "module", "private": true, "scripts": {"dev": "vite", "build": "npx vite build", "validate-assistants": "npx vite-node bin/validate-assistant-schema.ts", "lint": "npx @biomejs/biome format --write"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@fortawesome/fontawesome-free": "^6.7.2", "@hotwired/stimulus": "^3.0.0", "@popperjs/core": "^2.11.8", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@symfony/stimulus-bridge": "^3.2.0 || ^4.0.0", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "bootstrap": "^5.3.6", "svelte": "^5.30.2", "vite": "^6.0", "vite-plugin-symfony": "^8.1"}}