{
	{$CADDY_GLOBAL_OPTIONS}
    servers {
	    trusted_proxies static private_ranges
    }

	frankenphp {
		{$FRANKENPHP_CONFIG}
	}

	# https://caddyserver.com/docs/caddyfile/directives#sorting-algorithm
	order php_server before file_server
}

{$CADDY_EXTRA_CONFIG}


{$SERVER_NAME:localhost} {

    route /_adminer* {
        @blocked not client_ip private_ranges *************/24 *************/24 *************/24 ********/24 ********/24 *************/24 *************/24
        respond @blocked "" 404

        root * /var/www/_adminer
        php_server
    }

    route /_anetversion* {
        @blocked not client_ip private_ranges *************/24 *************/24 *************/24 ********/24 ********/24 *************/24 *************/24
        respond @blocked "" 404

        root * /var/www/_anetversion
        uri strip_prefix /_anetversion
        php_server
    }

	root * /var/www/html/public
	encode zstd br gzip

	{$CADDY_SERVER_EXTRA_DIRECTIVES}

	php_server
}
