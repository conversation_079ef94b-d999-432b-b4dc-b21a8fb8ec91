image: gitlab.alienor.net:5050/dev-ia/portail-tools-ia:dev

default:
    tags:
        - anetdev

cache:
    paths:
        - vendor/
        - assets/vendor/
    key:
        files:
            - composer.lock

stages:
    - build
    - test
    - build-image
    - analyze
    - sonarqube-check
    - sonarqube-vulnerability-report
    - deploy

build:
    stage: build
    script:
        - sudo -u www-data composer install
    artifacts:
        paths:
            - "vendor"
            - "assets/vendor"
            - "public/assets"
        expire_in: 10 minutes

php-cs-fixer:
    stage: test
    script: ./vendor/bin/php-cs-fixer fix --dry-run --diff

biome:
    stage: test
    allow_failure: true
    image:
        name: ghcr.io/biomejs/biome:latest
        entrypoint: [ "" ]
    script:
        - biome ci --reporter=gitlab --colors=off > /tmp/code-quality.json
        - cp /tmp/code-quality.json code-quality.json
    artifacts:
        paths:
            - code-quality.json    # Collect the code quality report as an artifact

phpunit:
    stage: test
    variables:
        SYMFONY_DEPRECATIONS_HELPER: disabled
        XDEBUG_MODE: coverage
    script:
        #- sudo -HEu www-data php bin/console doctrine:database:create --env=test
        #- sudo -HEu www-data php bin/console doctrine:migration:migrate --no-interaction --env=test
        - sudo -HEu www-data ./vendor/bin/phpunit --coverage-clover=coverage.xml  --log-junit report.xml
    artifacts:
        when: always
        paths:
            - coverage.xml
            - var/browser/source
            - var/log
        reports:
            junit: report.xml
        expire_in: 30 minutes

validate-assistant-schemas:
  stage: test
  image: node:24
  script:
    - npm install
    - npm run validate-assistants

build-image-preprod:
  stage: build-image
  image: docker:latest
  needs: ["phpunit"]
  variables:
    IMAGE_TAG: preprod
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - mkdir -p $HOME/.docker
    - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
  script:
    - docker compose -f compose.yaml -f compose.prod.yaml build php
    - echo "Image build successfully"
    - docker compose -f compose.yaml -f compose.prod.yaml push php
    - echo "Image push successfully"
  only:
    - preprod

build-image-prod:
  stage: build-image
  image: docker:latest
  needs: ["phpunit"]
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - mkdir -p $HOME/.docker
    - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
  script:
    - docker compose -f compose.yaml -f compose.prod.yaml build php
    - echo "Image build successfully"
    - docker compose -f compose.yaml -f compose.prod.yaml push php
    - echo "Image push successfully"
  only:
    - main

security-checker:
    stage: analyze
    image: jakzal/phpqa:php8.2
    script:
        - local-php-security-checker  --path=./composer.lock --format=junit > local-php-security-checker.xml
    allow_failure: true
    artifacts:
        when: always
        paths:
            - local-php-security-checker.xml
        reports:
            junit: local-php-security-checker.xml
        expire_in: 30 minutes

sonarqube-check:
    stage: sonarqube-check
    image:
        name: sonarsource/sonar-scanner-cli:5.0
        entrypoint: [""]
    variables:
        SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
        GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
    cache:
        key: "${CI_JOB_NAME}"
        paths:
            - .sonar/cache
    script:
        - sonar-scanner
    allow_failure: true
    only:
        - merge_requests
        - master
        - main
        - develop

sonarqube-vulnerability-report:
    stage: sonarqube-vulnerability-report
    script:
        - 'curl -u "${SONAR_TOKEN}:" "${SONAR_HOST_URL}/api/issues/gitlab_sast_export?projectKey=portail-tools-ia&branch=${CI_COMMIT_BRANCH}&pullRequest=${CI_MERGE_REQUEST_IID}" -o gl-sast-sonar-report.json'
    allow_failure: true
    only:
        - merge_requests
        - master
        - main
        - develop
    artifacts:
        expire_in: 1 day
        reports:
            sast: gl-sast-sonar-report.json
    dependencies:
        - sonarqube-check

deploy-preprod:
  image: gitlab.alienor.net:5050/dev-docker/docker-tools
  stage: deploy
  needs: ["build-image-preprod"]
  environment:
    name: staging
    url: https://portail-tools-ia-spp.int.alienor.net
  variables:
    GIT_STRATEGY: none
  script:
    - docker-tools update anet-dev web-4672_front_web -i gitlab.alienor.net:5050/dev-ia/portail-tools-ia:preprod
  only:
    - preprod

deploy-prod:
  image: gitlab.alienor.net:5050/dev-docker/docker-tools
  stage: deploy
  needs: ["build-image-prod"]
  environment:
    name: production
    url: https://portail-tools-ia-sp.int.alienor.net
  variables:
    GIT_STRATEGY: none
  script:
    - docker-tools update anet-dev web-4673_front_web -i gitlab.alienor.net:5050/dev-ia/portail-tools-ia:prod
  only:
    - main
