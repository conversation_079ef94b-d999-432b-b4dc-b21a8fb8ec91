import { defineConfig } from "vite";
import symfonyPlugin from "vite-plugin-symfony";
import { svelte } from '@sveltejs/vite-plugin-svelte'
import { dirname, resolve } from "node:path";
import { fileURLToPath } from "node:url";

const projectDir = dirname(fileURLToPath(import.meta.url));

export default defineConfig({
    plugins: [
      svelte(),
      symfonyPlugin({
          stimulus: true
      }),
    ],
    build: {
        rollupOptions: {
            input: {
                app: "./assets/app.ts"
            },
        }
    },
    resolve: {
        alias: {
            "~": resolve(projectDir, "assets"),
            "~project": projectDir,
        },
    },
});
