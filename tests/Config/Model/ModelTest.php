<?php

namespace App\Tests\Config\Model;

use App\Config\Model\Action;
use App\Config\Model\Api;
use App\Config\Model\AssistantConfig;
use App\Config\Model\Component;
use App\Config\Model\Page;
use App\Tests\Config\Builder\ActionBuilder;
use App\Tests\Config\Builder\ApiBuilder;
use App\Tests\Config\Builder\AssistantConfigBuilder;
use App\Tests\Config\Builder\ComponentBuilder;
use App\Tests\Config\Builder\PageBuilder;
use PHPUnit\Framework\TestCase;

class ModelTest extends TestCase
{
    public function test_action_creation_and_conversion(): void
    {
        $actionBuilder = (new ActionBuilder())
            ->withType('navigate')
            ->withTarget('page-id')
            ->withSetValues(['key' => 'value'])
            ->withSuccessMessage('Success!')
            ->withOptions(['option1' => 'value1']);

        $actionData = $actionBuilder->buildAsArray();
        $action = Action::fromArray($actionData);

        $this->assertEquals('navigate', $action->getType());
        $this->assertEquals('page-id', $action->getTarget());
        $this->assertEquals(['key' => 'value'], $action->getSetValues());
        $this->assertEquals('Success!', $action->getSuccessMessage());
        $this->assertEquals(['option1' => 'value1'], $action->getOptions());

        $convertedData = $action->toArray();
        $this->assertEquals($actionData, $convertedData);
    }

    public function test_api_creation_and_conversion(): void
    {
        $apiBuilder = (new ApiBuilder())
            ->withId('api-id')
            ->withEndpoint('/api/endpoint')
            ->withMethod('POST')
            ->withRequestMapping(['req' => 'value'])
            ->withResponseMapping(['res' => 'value']);

        $apiData = $apiBuilder->buildAsArray();
        $api = Api::fromArray($apiData);

        $this->assertEquals('/api/endpoint', $api->getEndpoint());
        $this->assertEquals('POST', $api->getMethod());
        $this->assertEquals(['req' => 'value'], $api->getRequestMapping());
        $this->assertEquals(['res' => 'value'], $api->getResponseMapping());

        $convertedData = $api->toArray();
        $this->assertEquals($apiData, $convertedData);
    }

    public function test_component_creation_and_conversion(): void
    {
        $actionBuilder = (new ActionBuilder())
            ->withType('navigate')
            ->withTarget('page-id');

        $componentBuilder = (new ComponentBuilder())
            ->withId('component-id')
            ->withComponent('Button')
            ->withProps(['label' => 'Click me'])
            ->withBind('data-bind')
            ->withAction($actionBuilder->build());

        $componentData = $componentBuilder->buildAsArray();
        $component = Component::fromArray($componentData);

        $this->assertEquals('component-id', $component->getId());
        $this->assertEquals('Button', $component->getComponent());
        $this->assertEquals(['label' => 'Click me'], $component->getProps());
        $this->assertEquals('data-bind', $component->getBind());
        $this->assertInstanceOf(Action::class, $component->getAction());
        $this->assertEquals('navigate', $component->getAction()->getType());

        $convertedData = $component->toArray();
        $this->assertEquals($componentData, $convertedData);
    }

    public function test_component_with_children(): void
    {
        $childBuilder = (new ComponentBuilder())
            ->withId('child-id')
            ->withComponent('Button')
            ->withProps(['label' => 'Child Button']);

        $parentBuilder = (new ComponentBuilder())
            ->withId('parent-id')
            ->withComponent('ButtonGroup')
            ->withProps(['orientation' => 'horizontal'])
            ->withChildren([$childBuilder->build()]);

        $componentData = $parentBuilder->buildAsArray();
        $component = Component::fromArray($componentData);

        $this->assertEquals('parent-id', $component->getId());
        $this->assertNotNull($component->getChildren());
        $this->assertCount(1, $component->getChildren());
        $this->assertEquals('child-id', $component->getChildren()[0]->getId());

        $convertedData = $component->toArray();
        $this->assertEquals($componentData, $convertedData);
    }

    public function test_page_creation_and_conversion(): void
    {
        $componentBuilder = (new ComponentBuilder())
            ->withId('component-id')
            ->withComponent('Text')
            ->withProps(['content' => 'Hello World']);

        $actionBuilder = (new ActionBuilder())
            ->withType('navigate')
            ->withTarget('next-page');

        $actionComponentBuilder = (new ComponentBuilder())
            ->withId('action-id')
            ->withComponent('Button')
            ->withProps(['label' => 'Next'])
            ->withAction($actionBuilder->build());

        $apiBuilder = (new ApiBuilder())
            ->withEndpoint('/api/data')
            ->withMethod('GET')
            ->withRequestMapping(['param' => 'value']);

        $pageBuilder = (new PageBuilder())
            ->withId('page-id')
            ->withTitle('Page Title')
            ->withComponents([$componentBuilder->build()])
            ->withActions([$actionComponentBuilder->build()])
            ->withApiCalls([$apiBuilder->build()]);

        $pageData = $pageBuilder->buildAsArray();
        $page = Page::fromArray($pageData);

        $this->assertEquals('page-id', $page->getId());
        $this->assertEquals('Page Title', $page->getTitle());
        $this->assertCount(1, $page->getComponents());
        $this->assertCount(1, $page->getActions());
        $this->assertCount(1, $page->getApiCalls());

        $convertedData = $page->toArray();
        $this->assertEquals($pageData, $convertedData);
    }

    public function test_assistant_config_creation_and_conversion(): void
    {
        $pageBuilder = (new PageBuilder())
            ->withId('page-id')
            ->withTitle('Page Title')
            ->withComponents([])
            ->withActions([]);

        $configBuilder = (new AssistantConfigBuilder())
            ->withId('assistant-id')
            ->withTitle('Assistant Title')
            ->withDescription('Assistant Description')
            ->withPages([$pageBuilder->build()])
            ->withAuthorizedModels(['gpt-4o', 'gpt-4o-mini']);

        $configData = $configBuilder->buildAsArray();
        $config = AssistantConfig::fromArray($configData);

        $this->assertEquals('assistant-id', $config->getId());
        $this->assertEquals('Assistant Title', $config->getTitle());
        $this->assertEquals('Assistant Description', $config->getDescription());
        $this->assertCount(1, $config->getPages());
        $this->assertEquals('page-id', $config->getPages()[0]->getId());

        $convertedData = $config->toArray();
        $this->assertEquals($configData, $convertedData);
    }
}
