<?php

namespace App\Tests\Config;

use App\Config\ConfigLoader;
use App\Config\Model\AssistantConfig;
use App\Service\ModelSelector;
use App\Tests\Config\Builder\AssistantConfigBuilder;
use App\Tests\Config\Builder\PageBuilder;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ConfigLoaderTest extends KernelTestCase
{
    private ConfigLoader $configLoader;
    private string $projectDir;
    private string $fixturesDir;

    protected function setUp(): void
    {
        self::bootKernel();

        $container = static::getContainer();
        $modelSelector = $container->get(ModelSelector::class);
        $this->projectDir = $container->getParameter('kernel.project_dir');
        $this->fixturesDir = $this->projectDir.'/tests/fixtures';

        // Créer une instance de ConfigLoader qui utilise le répertoire de fixtures
        $this->configLoader = new ConfigLoader($this->fixturesDir, $modelSelector);
    }

    public function test_load_assistant_config_returns_valid_config(): void
    {
        // Utiliser un assistant existant dans le projet
        $config = $this->configLoader->loadAssistantConfig('meeting-report-generator');

        $this->assertInstanceOf(AssistantConfig::class, $config);
        $this->assertEquals('meeting-report-generator', $config->getId());
        $this->assertEquals('Générateur de compte-rendu de réunion', $config->getTitle());
        $this->assertNotEmpty($config->getPages());
    }

    public function test_load_assistant_config_from_file_returns_valid_config(): void
    {
        $filePath = $this->fixturesDir.'/config/assistants/config/meeting-report-generator/assistant.json';
        $config = $this->configLoader->loadAssistantConfigFromFile($filePath);

        $this->assertInstanceOf(AssistantConfig::class, $config);
        $this->assertEquals('meeting-report-generator', $config->getId());
        $this->assertEquals('Générateur de compte-rendu de réunion', $config->getTitle());
        $this->assertNotEmpty($config->getPages());
    }

    public function test_load_assistant_config_throws_exception_for_nonexistent_assistant(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->configLoader->loadAssistantConfig('nonexistent-assistant');
    }

    public function test_load_assistant_config_from_file_throws_exception_for_nonexistent_file(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->configLoader->loadAssistantConfigFromFile('/nonexistent/path.json');
    }

    public function test_get_available_assistants_returns_array_with_assistants(): void
    {
        $assistants = $this->configLoader->getAvailableAssistants();

        $this->assertIsArray($assistants);
        $this->assertNotEmpty($assistants);
        $this->assertArrayHasKey('meeting-report-generator', $assistants);
        $this->assertEquals('Générateur de compte-rendu de réunion', $assistants['meeting-report-generator']);
    }

    public function test_assistant_config_to_array_conversion(): void
    {
        $filePath = $this->fixturesDir.'/config/assistants/config/meeting-report-generator/assistant.json';
        $config = $this->configLoader->loadAssistantConfigFromFile($filePath);
        $array = $config->toArray();

        $this->assertIsArray($array);
        $this->assertArrayHasKey('id', $array);
        $this->assertArrayHasKey('title', $array);
        $this->assertArrayHasKey('description', $array);
        $this->assertArrayHasKey('pages', $array);
        $this->assertEquals('meeting-report-generator', $array['id']);
    }

    public function test_invalid_json_throws_exception(): void
    {
        // Créer un fichier temporaire avec du JSON invalide
        $tempFile = tempnam(sys_get_temp_dir(), 'invalid_json_');
        file_put_contents($tempFile, '{invalid json}');

        $this->expectException(\InvalidArgumentException::class);

        try {
            $this->configLoader->loadAssistantConfigFromFile($tempFile);
        } finally {
            // Nettoyer le fichier temporaire
            unlink($tempFile);
        }
    }

    /**
     * Crée un fichier de configuration de test.
     *
     * @param string $filePath    Chemin du fichier à créer
     * @param string $assistantId Identifiant de l'assistant
     * @param string $title       Titre de l'assistant
     * @param string $description Description de l'assistant
     */
    private function createTestConfigFile(string $filePath, string $assistantId, string $title, string $description): void
    {
        $pageBuilder = (new PageBuilder())
            ->withId('test-page')
            ->withTitle('Test Page')
            ->withComponents([])
            ->withActions([]);

        $configBuilder = (new AssistantConfigBuilder())
            ->withId($assistantId)
            ->withTitle($title)
            ->withDescription($description)
            ->withPages([$pageBuilder->build()]);

        $configData = $configBuilder->buildAsArray();
        $jsonContent = json_encode($configData, JSON_PRETTY_PRINT);

        $dir = dirname($filePath);
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }

        file_put_contents($filePath, $jsonContent);
    }
}
