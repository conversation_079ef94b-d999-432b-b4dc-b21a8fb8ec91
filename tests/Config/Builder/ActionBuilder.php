<?php

namespace App\Tests\Config\Builder;

use App\Config\Model\Action;

class ActionBuilder
{
    private string $type = 'navigate';
    private ?string $target = 'page-id';
    private ?array $setValues = null;
    private ?string $successMessage = null;
    private ?array $options = null;

    public function withType(string $type): self
    {
        $clone = clone $this;
        $clone->type = $type;

        return $clone;
    }

    public function withTarget(?string $target): self
    {
        $clone = clone $this;
        $clone->target = $target;

        return $clone;
    }

    public function withSetValues(?array $setValues): self
    {
        $clone = clone $this;
        $clone->setValues = $setValues;

        return $clone;
    }

    public function withSuccessMessage(?string $successMessage): self
    {
        $clone = clone $this;
        $clone->successMessage = $successMessage;

        return $clone;
    }

    public function withOptions(?array $options): self
    {
        $clone = clone $this;
        $clone->options = $options;

        return $clone;
    }

    public function build(): Action
    {
        return new Action(
            $this->type,
            $this->target,
            $this->setValues,
            $this->successMessage,
            $this->options
        );
    }

    public function buildAsArray(): array
    {
        $result = [
            'type' => $this->type,
        ];

        if (null !== $this->target) {
            $result['target'] = $this->target;
        }

        if (null !== $this->setValues) {
            $result['setValues'] = $this->setValues;
        }

        if (null !== $this->successMessage) {
            $result['successMessage'] = $this->successMessage;
        }

        if (null !== $this->options) {
            $result['options'] = $this->options;
        }

        return $result;
    }
}
