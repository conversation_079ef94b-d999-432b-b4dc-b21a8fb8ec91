<?php

namespace App\Tests\Config\Builder;

use App\Config\Model\Api;

class ApiBuilder
{
    private string $id = 'api-id';
    private string $endpoint = '/api/endpoint';
    private string $method = 'POST';
    private array $requestMapping = ['param' => 'value'];
    private ?array $responseMapping = null;

    public function withId(string $id): self
    {
        $clone = clone $this;
        $clone->id = $id;

        return $clone;
    }

    public function withEndpoint(string $endpoint): self
    {
        $clone = clone $this;
        $clone->endpoint = $endpoint;

        return $clone;
    }

    public function withMethod(string $method): self
    {
        $clone = clone $this;
        $clone->method = $method;

        return $clone;
    }

    public function withRequestMapping(array $requestMapping): self
    {
        $clone = clone $this;
        $clone->requestMapping = $requestMapping;

        return $clone;
    }

    public function withResponseMapping(?array $responseMapping): self
    {
        $clone = clone $this;
        $clone->responseMapping = $responseMapping;

        return $clone;
    }

    public function build(): Api
    {
        return new Api(
            $this->id,
            $this->endpoint,
            $this->method,
            $this->requestMapping,
            $this->responseMapping
        );
    }

    public function buildAsArray(): array
    {
        $result = [
            'id' => $this->id,
            'endpoint' => $this->endpoint,
            'method' => $this->method,
            'requestMapping' => $this->requestMapping,
        ];

        if (null !== $this->responseMapping) {
            $result['responseMapping'] = $this->responseMapping;
        }

        return $result;
    }
}
