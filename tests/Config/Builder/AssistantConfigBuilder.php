<?php

namespace App\Tests\Config\Builder;

use App\Config\Model\AssistantConfig;
use App\Config\Model\ModelSelectionConfiguration;
use App\Config\Model\Page;

class AssistantConfigBuilder
{
    private string $id = 'assistant-id';
    private string $title = 'Assistant Title';
    private string $description = 'Assistant Description';
    private ModelSelectionConfiguration $modelSelectionConfiguration;
    private array $pages = [];
    private array $authorizedModels = [];

    public function __construct()
    {
        $this->modelSelectionConfiguration = new ModelSelectionConfiguration();
    }

    public function withId(string $id): self
    {
        $clone = clone $this;
        $clone->id = $id;

        return $clone;
    }

    public function withTitle(string $title): self
    {
        $clone = clone $this;
        $clone->title = $title;

        return $clone;
    }

    public function withDescription(string $description): self
    {
        $clone = clone $this;
        $clone->description = $description;

        return $clone;
    }

    public function withModelSelectionConfiguration(ModelSelectionConfiguration $modelSelectionConfiguration): self
    {
        $clone = clone $this;
        $clone->modelSelectionConfiguration = $modelSelectionConfiguration;

        return $clone;
    }

    public function withPages(array $pages): self
    {
        $clone = clone $this;
        $clone->pages = $pages;

        return $clone;
    }

    public function withAuthorizedModels(array $authorizedModels): self
    {
        $clone = clone $this;
        $clone->authorizedModels = $authorizedModels;

        return $clone;
    }

    public function build(): AssistantConfig
    {
        return new AssistantConfig(
            $this->id,
            $this->title,
            $this->description,
            $this->modelSelectionConfiguration,
            $this->pages,
        );
    }

    public function buildAsArray(): array
    {
        $pagesArray = [];
        foreach ($this->pages as $page) {
            if ($page instanceof Page) {
                $pagesArray[] = $page->toArray();
            } elseif ($page instanceof PageBuilder) {
                $pagesArray[] = $page->buildAsArray();
            } else {
                $pagesArray[] = $page;
            }
        }

        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'modelSelectionConfiguration' => $this->modelSelectionConfiguration->toArray(),
            'pages' => $pagesArray,
        ];
    }
}
