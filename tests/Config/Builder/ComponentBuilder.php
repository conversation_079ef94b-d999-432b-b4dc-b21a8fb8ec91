<?php

namespace App\Tests\Config\Builder;

use App\Config\Model\Action;
use App\Config\Model\Component;

class ComponentBuilder
{
    private string $id = 'component-id';
    private string $component = 'Button';
    private array $props = ['label' => 'Click me'];
    private ?string $bind = null;
    private ?array $children = null;
    private ?Action $action = null;

    public function withId(string $id): self
    {
        $clone = clone $this;
        $clone->id = $id;

        return $clone;
    }

    public function withComponent(string $component): self
    {
        $clone = clone $this;
        $clone->component = $component;

        return $clone;
    }

    public function withProps(array $props): self
    {
        $clone = clone $this;
        $clone->props = $props;

        return $clone;
    }

    public function withBind(?string $bind): self
    {
        $clone = clone $this;
        $clone->bind = $bind;

        return $clone;
    }

    public function withChildren(?array $children): self
    {
        $clone = clone $this;
        $clone->children = $children;

        return $clone;
    }

    public function withAction(?Action $action): self
    {
        $clone = clone $this;
        $clone->action = $action;

        return $clone;
    }

    public function build(): Component
    {
        return new Component(
            $this->id,
            $this->component,
            $this->props,
            $this->bind,
            $this->children,
            $this->action
        );
    }

    public function buildAsArray(): array
    {
        $result = [
            'id' => $this->id,
            'component' => $this->component,
            'props' => $this->props,
        ];

        if (null !== $this->bind) {
            $result['bind'] = $this->bind;
        }

        if (null !== $this->children) {
            $childrenArray = [];
            foreach ($this->children as $child) {
                if ($child instanceof Component) {
                    $childrenArray[] = $child->toArray();
                } elseif ($child instanceof ComponentBuilder) {
                    $childrenArray[] = $child->buildAsArray();
                } else {
                    $childrenArray[] = $child;
                }
            }
            $result['children'] = $childrenArray;
        }

        if (null !== $this->action) {
            $result['action'] = $this->action->toArray();
        }

        return $result;
    }
}
