<?php

namespace App\Tests\Config\Builder;

use App\Config\Model\Api;
use App\Config\Model\Component;
use App\Config\Model\Page;

class PageBuilder
{
    private string $id = 'page-id';
    private string $title = 'Page Title';
    private array $components = [];
    private array $actions = [];
    private array $apiCalls = [];

    public function withId(string $id): self
    {
        $clone = clone $this;
        $clone->id = $id;

        return $clone;
    }

    public function withTitle(string $title): self
    {
        $clone = clone $this;
        $clone->title = $title;

        return $clone;
    }

    public function withComponents(array $components): self
    {
        $clone = clone $this;
        $clone->components = $components;

        return $clone;
    }

    public function withActions(array $actions): self
    {
        $clone = clone $this;
        $clone->actions = $actions;

        return $clone;
    }

    public function withApiCalls(array $apiCalls): self
    {
        $clone = clone $this;
        $clone->apiCalls = $apiCalls;

        return $clone;
    }

    public function build(): Page
    {
        return new Page(
            $this->id,
            $this->title,
            $this->components,
            $this->actions,
            $this->apiCalls,
        );
    }

    public function buildAsArray(): array
    {
        $componentsArray = [];
        foreach ($this->components as $component) {
            if ($component instanceof Component) {
                $componentsArray[] = $component->toArray();
            } elseif ($component instanceof ComponentBuilder) {
                $componentsArray[] = $component->buildAsArray();
            } else {
                $componentsArray[] = $component;
            }
        }

        $actionsArray = [];
        foreach ($this->actions as $action) {
            if ($action instanceof Component) {
                $actionsArray[] = $action->toArray();
            } elseif ($action instanceof ComponentBuilder) {
                $actionsArray[] = $action->buildAsArray();
            } else {
                $actionsArray[] = $action;
            }
        }

        $apiCallsArray = [];
        foreach ($this->apiCalls as $apiCall) {
            if ($apiCall instanceof Api) {
                $apiCallsArray[] = $apiCall->toArray();
            } elseif ($apiCall instanceof ApiBuilder) {
                $apiCallsArray[] = $apiCall->buildAsArray();
            } else {
                $apiCallsArray[] = $apiCall;
            }
        }

        $result = [
            'id' => $this->id,
            'title' => $this->title,
            'components' => $componentsArray,
            'actions' => $actionsArray,
            'apiCalls' => $apiCallsArray,
        ];

        return $result;
    }
}
