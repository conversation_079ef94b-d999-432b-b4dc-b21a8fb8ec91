{"id": "meeting-report-generator", "title": "Générateur de compte-rendu de réunion", "description": "Assistant pour générer un compte-rendu structuré à partir de texte ou d'audio", "modelSelectionConfiguration": {"allowedCategories": ["normal"], "allowedLocations": ["internal"], "defaultModel": "gpt-4o"}, "pages": [{"id": "input-choice", "title": "Source du compte-rendu", "components": [{"id": "source-selection-prompt", "component": "Text", "props": {"content": "Choisissez votre source pour générer le compte-rendu :", "variant": "heading", "size": "medium"}}, {"id": "source-selection-buttons", "component": "ButtonGroup", "props": {"orientation": "horizontal", "gap": "medium", "alignment": "center"}, "children": [{"id": "text-button", "component": "<PERSON><PERSON>", "props": {"label": "Texte", "variant": "primary", "size": "large"}, "action": {"type": "navigate", "target": "text-input", "setValues": {"input-type": "text"}}}, {"id": "audio-button", "component": "<PERSON><PERSON>", "props": {"label": "Audio", "variant": "primary", "size": "large"}, "action": {"type": "navigate", "target": "audio-upload", "setValues": {"input-type": "audio"}}}]}]}, {"id": "text-input", "title": "<PERSON><PERSON> du texte", "components": [{"id": "meeting-text-input", "component": "RichTextarea", "props": {"id": "meeting-text", "label": "Contenu de la réunion", "placeholder": "<PERSON><PERSON>z ici les notes ou la transcription de votre réunion...", "rows": 10, "required": true, "autofocus": true}, "bind": "meeting-text"}, {"id": "tone-selector", "component": "Select", "props": {"id": "tone", "label": "Ton du compte-rendu", "options": [{"value": "formal", "label": "Formel"}, {"value": "neutral", "label": "Neutre"}, {"value": "casual", "label": "Décontracté"}], "defaultValue": "neutral"}, "bind": "tone"}], "actions": [{"id": "back-button", "component": "<PERSON><PERSON>", "props": {"label": "Retour", "variant": "secondary"}, "action": {"type": "navigate", "target": "input-choice"}}, {"id": "submit-button", "component": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON><PERSON> le compte-rendu", "variant": "primary"}, "action": {"type": "submit", "target": "result"}}], "api": {"endpoint": "/api/generate-report", "method": "POST", "requestMapping": {"inputType": "input-type", "content": ["meeting-text", "audio-file"], "tone": "tone"}, "responseMapping": {"report": "generated-report"}}}]}