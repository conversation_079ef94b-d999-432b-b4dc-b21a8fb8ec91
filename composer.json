{"name": "symfony/skeleton", "type": "project", "license": "MIT", "description": "A minimal Symfony project recommended to create bare bones applications", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.4.1", "ext-ctype": "*", "ext-iconv": "*", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.13", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "^3.3", "league/flysystem-aws-s3-v3": "^3.29", "league/flysystem-bundle": "^3.4", "pentatrion/vite-bundle": "^8.1", "phpdocumentor/reflection-docblock": "^5.6", "phpstan/phpdoc-parser": "^2.1", "runtime/frankenphp-symfony": "^0.2.0", "symfony/console": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/flex": "^2", "symfony/framework-bundle": "7.3.*", "symfony/mailer": "7.3.*", "symfony/monolog-bundle": "^3.10", "symfony/property-access": "7.3.*", "symfony/property-info": "7.3.*", "symfony/runtime": "7.3.*", "symfony/security-bundle": "7.3.*", "symfony/serializer": "7.3.*", "symfony/stimulus-bundle": "^2.25", "symfony/twig-bundle": "7.3.*", "symfony/uid": "7.3.*", "symfony/yaml": "7.3.*", "twig/extra-bundle": "^3.0", "twig/twig": "^3.21"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*", "docker": true}}, "require-dev": {"dama/doctrine-test-bundle": "^8.2", "friendsofphp/php-cs-fixer": "^3.65", "phpstan/phpstan": "^2.0", "phpstan/phpstan-doctrine": "^2.0", "phpstan/phpstan-symfony": "^2.0", "phpunit/phpunit": "^9.5", "symfony/browser-kit": "7.3.*", "symfony/css-selector": "7.3.*", "symfony/maker-bundle": "^1.61", "symfony/phpunit-bridge": "^7.1", "symfony/stopwatch": "7.3.*", "symfony/web-profiler-bundle": "7.3.*", "vincentlanglet/twig-cs-fixer": "^3.5"}}