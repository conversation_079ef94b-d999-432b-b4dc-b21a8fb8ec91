services:
    php:
        image: gitlab.alienor.net:5050/dev-ia/portail-tools-ia:dev
        environment:
            SERVER_NAME: ${SERVER_NAME:-localhost}, php:80
            TRUSTED_PROXIES: ${TRUSTED_PROXIES:-*********/8,10.0.0.0/8,**********/12,***********/16}
            TRUSTED_HOSTS: ${TRUSTED_HOSTS:-^${SERVER_NAME:-example\.com|localhost}|php$$}
        volumes:
            - caddy_data:/data
            - caddy_config:/config
        ports:
            # HTTP
            - target: 80
              published: ${HTTP_PORT:-80}
              protocol: tcp
            # HTTPS
            - target: 443
              published: ${HTTPS_PORT:-443}
              protocol: tcp
            # HTTP/3
            - target: 443
              published: ${HTTP3_PORT:-443}
              protocol: udp

    database:
        image: postgres:${POSTGRES_VERSION:-17.5}-alpine
        environment:
            - POSTGRES_DB=${POSTGRES_DB:-app}
            # You should definitely change the password in production
            - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-!ChangeMe!}
            - POSTGRES_USER=${POSTGRES_USER:-app}
            - TZ=Europe/Paris
            - PGTZ=Europe/Paris
        volumes:
            - db_data:/var/lib/postgresql/data
            # you may use a bind-mounted host directory instead, so that it is harder to accidentally remove the volume and lose all your data!
            # - ./api/docker/db/data:/var/lib/postgresql/data

    redis:
        image: redis:alpine
        volumes:
            - redis_data:/data

volumes:
    caddy_data:
    caddy_config:
    db_data:
    redis_data:
