# https://taskfile.dev

version: '3'

vars:
  GITLAB_PROJECT: 'dev-ia/portail-tools-ia'
  PREPROD_SERVER: 'aquitem-preprod'
  PREPROD_SERVICE: 'web-xxxx_front_web'
  PROD_SERVER: 'aquitem-prod'
  PROD_SERVICE: 'web-xxxx_front_web'

includes:
  docker: https://gitlab.alienor.net/-/snippets/11/raw/main/Taskfile.yml

tasks:
  default:
    cmds:
      - task: docker:exec
    silent: true

  lint:
    desc: Lint php and twig files
    cmds:
      - 'docker compose exec -e PHP_CS_FIXER_IGNORE_ENV=1 php sudo -Eu www-data php vendor/bin/php-cs-fixer fix'
      - 'docker compose exec php sudo -Eu www-data php vendor/bin/twig-cs-fixer lint --fix templates'
      - 'npm run lint'

  test:
    desc: Run all tests
    cmds:
      - '{{.DOCKER_PHP}} bin/phpunit'
  reset-db:
      desc: Reset database
      cmds:
          - '{{.DOCKER_SYMFONY}} doctrine:database:drop --force --if-exists'
          - '{{.DOCKER_SYMFONY}} doctrine:database:create'
          - '{{.DOCKER_SYMFONY}} doctrine:migration:migrate --no-interaction'
          - '{{.DOCKER_SYMFONY}} doctrine:fixtures:load --no-interaction'
  validate-assistants:
    desc: Validate assistant JSON schemas
    cmds:
      - 'npm run validate-assistants'
