#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import schema from '../config/assistants/schema/assistant-schema';

// Obtenir l'équivalent de __dirname en ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialiser le validateur
const ajv = new Ajv({ allErrors: true });
addFormats(ajv);

// Charger le schéma
const validate = ajv.compile(schema);

// Fonction pour valider un fichier
function validateFile(filePath) {
    try {
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const data = JSON.parse(fileContent);
        const valid = validate(data);

        if (!valid) {
            console.error(`\x1b[31mValidation failed for ${filePath}:\x1b[0m`);
            validate.errors.forEach((error) => {
                console.error(`  - ${error.instancePath} ${error.message}`);
            });
            return false;
        }

        console.log(`\x1b[32m✓ ${filePath} is valid\x1b[0m`);
        return true;
    } catch (error) {
        console.error(`\x1b[31mError processing ${filePath}: ${error.message}\x1b[0m`);
        return false;
    }
}

// Fonction récursive pour trouver tous les fichiers assistant.json
function findAssistantJsonFiles(dir) {
    const files = [];
    const entries = fs.readdirSync(dir, { withFileTypes: true });

    for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);

        if (entry.isDirectory()) {
            // Si c'est un répertoire, chercher le fichier assistant.json à l'intérieur
            const assistantJsonPath = path.join(fullPath, 'assistant.json');
            if (fs.existsSync(assistantJsonPath)) {
                files.push(assistantJsonPath);
            }

            // Continuer la recherche récursive dans les sous-répertoires
            files.push(...findAssistantJsonFiles(fullPath));
        }
    }

    return files;
}

// Valider un fichier spécifique ou tous les fichiers
const targetFile = process.argv[2];
if (targetFile) {
    const success = validateFile(targetFile);
    process.exit(success ? 0 : 1);
} else {
    const assistantsDir = path.join(__dirname, '../config/assistants/config');
    const files = findAssistantJsonFiles(assistantsDir);

    const results = files.map(validateFile);
    const success = results.every(Boolean);

    console.log(
        `\nValidated ${files.length} files, ${results.filter(Boolean).length} valid, ${results.filter((r) => !r).length} invalid`
    );
    process.exit(success ? 0 : 1);
}
